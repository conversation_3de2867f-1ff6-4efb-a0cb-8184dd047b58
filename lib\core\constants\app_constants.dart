class AppConstants {
  // Uygulama Bilgileri
  static const String appName = 'Partizan';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'Asker Takip Uygulaması';
  
  // API Endpoints
  static const String baseUrl = 'https://api.partizan-military.com';
  static const String authEndpoint = '/auth';
  static const String trackingEndpoint = '/tracking';
  static const String commanderEndpoint = '/commander';
  static const String messagingEndpoint = '/messaging';
  
  // WebSocket
  static const String wsUrl = 'wss://ws.partizan-military.com';
  
  // Güvenlik
  static const String encryptionKey = 'PARTIZAN_MILITARY_2024_SECURE_KEY';
  static const String tokenKey = 'auth_token';
  static const String userIdKey = 'user_id';
  static const String userRoleKey = 'user_role';
  
  // Roller
  static const String soldierRole = 'ASKER';
  static const String commanderRole = 'KOMUTAN';
  static const String adminRole = 'ADMIN';
  
  // Konum Ayarları
  static const double locationAccuracy = 5.0; // metre
  static const int locationUpdateInterval = 5000; // milisaniye
  static const int fastestLocationInterval = 2000; // milisaniye
  
  // Harita Ayarları
  static const double defaultZoom = 15.0;
  static const double maxZoom = 20.0;
  static const double minZoom = 5.0;
  
  // Varsayılan Koordinatlar (İstanbul)
  static const double defaultLatitude = 41.0082;
  static const double defaultLongitude = 28.9784;
  
  // Renkler (Hex)
  static const String primaryColor = '#1A1A1A';
  static const String secondaryColor = '#2D5016';
  static const String accentColor = '#4CAF50';
  static const String errorColor = '#F44336';
  static const String warningColor = '#FF9800';
  static const String successColor = '#4CAF50';
  
  // Mesajlaşma
  static const int maxMessageLength = 500;
  static const int messageHistoryLimit = 100;
  
  // Dosya Boyutları
  static const int maxImageSize = 5 * 1024 * 1024; // 5MB
  static const int maxVideoSize = 50 * 1024 * 1024; // 50MB
  
  // Timeout Süreleri
  static const int connectionTimeout = 30000; // milisaniye
  static const int receiveTimeout = 30000; // milisaniye
  static const int sendTimeout = 30000; // milisaniye
  
  // Animasyon Süreleri
  static const int shortAnimationDuration = 200;
  static const int mediumAnimationDuration = 500;
  static const int longAnimationDuration = 1000;
  
  // Bildirim Ayarları
  static const String notificationChannelId = 'partizan_notifications';
  static const String notificationChannelName = 'Partizan Bildirimleri';
  static const String notificationChannelDescription = 'Partizan uygulaması bildirimleri';
  
  // Hata Mesajları (Türkçe)
  static const String networkError = 'Ağ bağlantısı hatası';
  static const String serverError = 'Sunucu hatası';
  static const String authError = 'Kimlik doğrulama hatası';
  static const String locationError = 'Konum erişim hatası';
  static const String permissionError = 'İzin hatası';
  static const String unknownError = 'Bilinmeyen hata';
  
  // Başarı Mesajları
  static const String loginSuccess = 'Giriş başarılı';
  static const String logoutSuccess = 'Çıkış başarılı';
  static const String locationUpdateSuccess = 'Konum güncellendi';
  static const String messageSentSuccess = 'Mesaj gönderildi';
  
  // Uyarı Mesajları
  static const String locationPermissionWarning = 'Konum izni gerekli';
  static const String networkConnectionWarning = 'İnternet bağlantısını kontrol edin';
  static const String batteryOptimizationWarning = 'Pil optimizasyonunu kapatın';
  
  // Shared Preferences Keys
  static const String isFirstLaunch = 'is_first_launch';
  static const String isDarkMode = 'is_dark_mode';
  static const String isLocationEnabled = 'is_location_enabled';
  static const String lastKnownLatitude = 'last_known_latitude';
  static const String lastKnownLongitude = 'last_known_longitude';
  static const String userSettings = 'user_settings';
  
  // Asset Paths
  static const String logoPath = 'assets/images/logo.png';
  static const String splashLogoPath = 'assets/images/splash_logo.png';
  static const String militaryIconPath = 'assets/icons/military_icon.svg';
  static const String soldierIconPath = 'assets/icons/soldier_icon.svg';
  static const String commanderIconPath = 'assets/icons/commander_icon.svg';
  static const String mapMarkerPath = 'assets/icons/map_marker.svg';
  
  // Font Families
  static const String primaryFont = 'Roboto';
  static const String militaryFont = 'Military';
  
  // Text Sizes
  static const double titleTextSize = 24.0;
  static const double subtitleTextSize = 18.0;
  static const double bodyTextSize = 16.0;
  static const double captionTextSize = 14.0;
  static const double smallTextSize = 12.0;
  
  // Spacing
  static const double smallSpacing = 8.0;
  static const double mediumSpacing = 16.0;
  static const double largeSpacing = 24.0;
  static const double extraLargeSpacing = 32.0;
  
  // Border Radius
  static const double smallRadius = 4.0;
  static const double mediumRadius = 8.0;
  static const double largeRadius = 16.0;
  static const double circularRadius = 50.0;
  
  // Elevation
  static const double lowElevation = 2.0;
  static const double mediumElevation = 4.0;
  static const double highElevation = 8.0;
  
  // Icon Sizes
  static const double smallIconSize = 16.0;
  static const double mediumIconSize = 24.0;
  static const double largeIconSize = 32.0;
  static const double extraLargeIconSize = 48.0;
}
