import 'dart:async';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import '../constants/app_constants.dart';

class LocationService {
  static LocationService? _instance;
  static LocationService get instance => _instance ??= LocationService._();
  LocationService._();

  StreamSubscription<Position>? _positionStreamSubscription;
  Position? _currentPosition;
  bool _isTracking = false;
  
  // Konum güncellemeleri için stream controller
  final StreamController<Position> _locationController = 
      StreamController<Position>.broadcast();
  
  Stream<Position> get locationStream => _locationController.stream;
  Position? get currentPosition => _currentPosition;
  bool get isTracking => _isTracking;

  /// Konum servisini başlat
  static Future<void> initialize() async {
    await instance._checkPermissions();
  }

  /// Konum izinlerini kontrol et
  Future<bool> _checkPermissions() async {
    try {
      // Konum servisi açık mı kontrol et
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        throw LocationException('Konum servisi kapalı');
      }

      // İzin durumunu kontrol et
      LocationPermission permission = await Geolocator.checkPermission();
      
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          throw LocationException('Konum izni reddedildi');
        }
      }

      if (permission == LocationPermission.deniedForever) {
        throw LocationException('Konum izni kalıcı olarak reddedildi');
      }

      // Arka plan konum izni (Android için)
      if (await Permission.locationAlways.isDenied) {
        await Permission.locationAlways.request();
      }

      return true;
    } catch (e) {
      throw LocationException('İzin kontrolü başarısız: $e');
    }
  }

  /// Mevcut konumu al
  Future<Position> getCurrentLocation() async {
    try {
      await _checkPermissions();
      
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );
      
      _currentPosition = position;
      return position;
    } catch (e) {
      throw LocationException('Konum alınamadı: $e');
    }
  }

  /// Konum takibini başlat
  Future<void> startTracking() async {
    if (_isTracking) return;
    
    try {
      await _checkPermissions();
      
      const LocationSettings locationSettings = LocationSettings(
        accuracy: LocationAccuracy.high,
        distanceFilter: 5, // 5 metre hareket ettiğinde güncelle
      );

      _positionStreamSubscription = Geolocator.getPositionStream(
        locationSettings: locationSettings,
      ).listen(
        (Position position) {
          _currentPosition = position;
          _locationController.add(position);
        },
        onError: (error) {
          _locationController.addError(
            LocationException('Konum takibi hatası: $error'),
          );
        },
      );

      _isTracking = true;
    } catch (e) {
      throw LocationException('Konum takibi başlatılamadı: $e');
    }
  }

  /// Konum takibini durdur
  Future<void> stopTracking() async {
    if (!_isTracking) return;
    
    await _positionStreamSubscription?.cancel();
    _positionStreamSubscription = null;
    _isTracking = false;
  }

  /// İki konum arasındaki mesafeyi hesapla (metre)
  double calculateDistance(
    double startLatitude,
    double startLongitude,
    double endLatitude,
    double endLongitude,
  ) {
    return Geolocator.distanceBetween(
      startLatitude,
      startLongitude,
      endLatitude,
      endLongitude,
    );
  }

  /// İki konum arasındaki yönü hesapla (derece)
  double calculateBearing(
    double startLatitude,
    double startLongitude,
    double endLatitude,
    double endLongitude,
  ) {
    return Geolocator.bearingBetween(
      startLatitude,
      startLongitude,
      endLatitude,
      endLongitude,
    );
  }

  /// Konum doğruluğunu kontrol et
  bool isLocationAccurate(Position position) {
    return position.accuracy <= AppConstants.locationAccuracy;
  }

  /// Konum verilerini formatla
  Map<String, dynamic> formatLocationData(Position position) {
    return {
      'latitude': position.latitude,
      'longitude': position.longitude,
      'accuracy': position.accuracy,
      'altitude': position.altitude,
      'heading': position.heading,
      'speed': position.speed,
      'speedAccuracy': position.speedAccuracy,
      'timestamp': position.timestamp.toIso8601String(),
    };
  }

  /// Konum geçmişini temizle
  void clearLocationHistory() {
    _currentPosition = null;
  }

  /// Konum servisini temizle
  Future<void> dispose() async {
    await stopTracking();
    await _locationController.close();
    _currentPosition = null;
  }

  /// Konum servisi durumunu kontrol et
  Future<bool> isLocationServiceEnabled() async {
    return await Geolocator.isLocationServiceEnabled();
  }

  /// İzin durumunu al
  Future<LocationPermission> getPermissionStatus() async {
    return await Geolocator.checkPermission();
  }

  /// Konum ayarlarını aç
  Future<bool> openLocationSettings() async {
    return await Geolocator.openLocationSettings();
  }

  /// Uygulama ayarlarını aç
  Future<bool> openAppSettings() async {
    return await Geolocator.openAppSettings();
  }

  /// Son bilinen konumu al
  Future<Position?> getLastKnownPosition() async {
    try {
      return await Geolocator.getLastKnownPosition();
    } catch (e) {
      return null;
    }
  }

  /// Konum verilerini doğrula
  bool validateLocationData(Map<String, dynamic> data) {
    try {
      final latitude = data['latitude'] as double?;
      final longitude = data['longitude'] as double?;
      
      if (latitude == null || longitude == null) return false;
      
      // Geçerli koordinat aralıkları
      if (latitude < -90 || latitude > 90) return false;
      if (longitude < -180 || longitude > 180) return false;
      
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Konum güncellemelerini dinle
  StreamSubscription<Position> listenToLocationUpdates(
    Function(Position) onLocationUpdate,
    Function(LocationException)? onError,
  ) {
    return locationStream.listen(
      onLocationUpdate,
      onError: (error) {
        if (onError != null && error is LocationException) {
          onError(error);
        }
      },
    );
  }

  /// Arka plan konum takibi için ayarlar
  Future<void> configureBackgroundLocation() async {
    try {
      // Android için arka plan konum izni
      if (await Permission.locationAlways.isDenied) {
        final status = await Permission.locationAlways.request();
        if (status != PermissionStatus.granted) {
          throw LocationException('Arka plan konum izni gerekli');
        }
      }

      // iOS için arka plan konum kullanımı
      if (await Permission.locationWhenInUse.isDenied) {
        await Permission.locationWhenInUse.request();
      }
    } catch (e) {
      throw LocationException('Arka plan konum ayarları başarısız: $e');
    }
  }

  /// Konum verilerini şifrele (güvenlik için)
  Map<String, dynamic> encryptLocationData(Position position) {
    // Bu method SecurityService ile entegre edilecek
    return formatLocationData(position);
  }

  /// Acil durum konumu gönder
  Future<void> sendEmergencyLocation() async {
    try {
      final position = await getCurrentLocation();
      // Bu method API servisi ile entegre edilecek
      // Acil durum konumu sunucuya gönderilecek
    } catch (e) {
      throw LocationException('Acil durum konumu gönderilemedi: $e');
    }
  }
}

/// Konum exception sınıfı
class LocationException implements Exception {
  final String message;
  LocationException(this.message);
  
  @override
  String toString() => 'LocationException: $message';
}
