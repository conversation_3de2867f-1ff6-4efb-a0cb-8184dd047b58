# 🚀 راهنمای کامل GitHub Actions Setup

## 📋 مرحله 1: ایجاد GitHub Repository

### 1.1 ایجاد Repository
```bash
1. به GitHub.com بروید
2. "New Repository" کلیک کنید
3. نام: partizan-military-app
4. Description: 🎯 Partizan - Real-time Military Tracking Application
5. Public یا Private انتخاب کنید
6. "Create Repository" کلیک کنید
```

### 1.2 آپلود فایل‌های پروژه
```bash
# روش 1: GitHub Web Interface
1. "uploading an existing file" کلیک کنید
2. تمام فایل‌های پروژه را drag & drop کنید
3. Commit message: "🎯 Initial Partizan project setup"
4. "Commit changes" کلیک کنید

# روش 2: Git Command Line (اگر Git نصب است)
git init
git add .
git commit -m "🎯 Initial Partizan project setup"
git branch -M main
git remote add origin https://github.com/USERNAME/partizan-military-app.git
git push -u origin main
```

## 🔄 مرحله 2: فعال‌سازی GitHub Actions

### 2.1 Actions Tab
```bash
1. Repository > Actions tab
2. "I understand my workflows, go ahead and enable them"
3. GitHub Actions فعال می‌شود
```

### 2.2 اولین Build
```bash
1. Actions > "🚀 Build Partizan APK" workflow
2. "Run workflow" کلیک کنید
3. Branch: main
4. Version: 1.0.0 (اختیاری)
5. "Run workflow" کلیک کنید
```

## ⏱️ مرحله 3: منتظر Build بمانید

### 3.1 Build Process (20-30 دقیقه)
```
📊 مراحل Build:
├── 📥 Checkout Repository (30 ثانیه)
├── ☕ Setup Java (1 دقیقه)
├── 🐦 Setup Flutter (3 دقیقه)
├── 📦 Get Dependencies (2 دقیقه)
├── 🔍 Analyze Code (1 دقیقه)
├── 🧪 Run Tests (2 دقیقه)
├── 🏗️ Build APK Debug (5 دقیقه)
├── 🏗️ Build APK Release (8 دقیقه)
├── 🌐 Build Web (3 دقیقه)
└── 🎉 Create Release (2 دقیقه)

⏱️ کل زمان: ~25 دقیقه
```

### 3.2 نظارت بر Build
```bash
1. Actions tab > Running workflow
2. "🚀 Build Partizan APK" کلیک کنید
3. Real-time logs مشاهده کنید
4. ✅ یا ❌ نتیجه را ببینید
```

## 📱 مرحله 4: دانلود APK

### 4.1 از Artifacts (فوری)
```bash
1. Actions > Completed workflow
2. Scroll down > Artifacts section
3. "partizan-universal-apk" دانلود کنید
4. ZIP file extract کنید
5. app-release.apk آماده است!
```

### 4.2 از Releases (بهتر)
```bash
1. Repository > Releases tab
2. Latest release کلیک کنید
3. Assets section:
   - partizan-universal.apk (همه گوشی‌ها)
   - partizan-arm64.apk (گوشی‌های جدید)
   - partizan-arm.apk (گوشی‌های قدیمی)
4. APK مناسب را دانلود کنید
```

## 🔗 مرحله 5: ایجاد لینک دانلود

### 5.1 Direct Download Link
```bash
# فرمت لینک مستقیم:
https://github.com/USERNAME/partizan-military-app/releases/download/TAG/partizan-universal.apk

# مثال:
https://github.com/hakan123456h/partizan-military-app/releases/download/v2024-01-15-14-30/partizan-universal.apk
```

### 5.2 QR Code Generator
```bash
1. به qr-code-generator.com بروید
2. لینک APK را paste کنید
3. QR Code generate کنید
4. QR Code را save کنید
5. با سربازان share کنید
```

## 📲 مرحله 6: توزیع به سربازان

### 6.1 پیام نمونه برای سربازان
```
🎯 دانلود اپلیکیشن پارتیزان

📱 لینک دانلود:
[DOWNLOAD_LINK]

🔐 حساب تست:
نام کاربری: asker
رمز عبور: partizan2024

📋 مراحل نصب:
1. لینک را باز کنید
2. APK دانلود کنید
3. "منابع نامعلوم" فعال کنید
4. نصب کنید
5. مجوزها بدهید
6. وارد شوید

⚠️ مهم: فقط از لینک رسمی دانلود کنید
```

### 6.2 کانال‌های توزیع
```bash
✅ توصیه شده:
- تلگرام گروه
- WhatsApp گروه
- SMS مستقیم
- ایمیل رسمی

❌ توصیه نمی‌شود:
- شبکه‌های اجتماعی عمومی
- فروشگاه‌های غیررسمی
- سایت‌های نامعلوم
```

## 🔄 مرحله 7: به‌روزرسانی خودکار

### 7.1 Auto-Build Setup
```bash
هر push به main branch:
├── ✅ خودکار build می‌شود
├── ✅ APK جدید تولید می‌شود
├── ✅ Release جدید ایجاد می‌شود
└── ✅ لینک دانلود به‌روز می‌شود
```

### 7.2 Manual Build
```bash
1. Repository > Actions
2. "🚀 Build Partizan APK"
3. "Run workflow"
4. منتظر بمانید
5. APK جدید آماده!
```

## 🛠️ مرحله 8: رفع مشکلات

### 8.1 Build Failed
```bash
❌ مشکلات رایج:
- Gradle sync error
- Flutter version mismatch
- Dependency conflicts
- Memory issues

✅ راه‌حل:
1. Actions > Failed workflow
2. Logs بررسی کنید
3. Error message پیدا کنید
4. مشکل را برطرف کنید
5. دوباره push کنید
```

### 8.2 APK نصب نمی‌شود
```bash
❌ علت‌های احتمالی:
- Unknown sources غیرفعال
- Storage کم
- Android version قدیمی
- Corrupted download

✅ راه‌حل:
- Settings > Security > Unknown sources ✅
- 200MB فضای خالی
- Android 5.0+ (API 21)
- دوباره دانلود کنید
```

## 📊 مرحله 9: آمار و نظارت

### 9.1 Download Statistics
```bash
Repository > Insights > Traffic:
- Views: تعداد بازدید
- Clones: تعداد clone
- Releases: تعداد دانلود APK
```

### 9.2 User Feedback
```bash
Repository > Issues:
- Bug reports
- Feature requests
- User questions
- Technical support
```

## 🎯 خلاصه نهایی

### ✅ چک‌لیست تکمیل:
- [ ] GitHub Repository ایجاد شد
- [ ] فایل‌های پروژه آپلود شد
- [ ] GitHub Actions فعال شد
- [ ] اولین build موفق بود
- [ ] APK دانلود شد
- [ ] لینک دانلود آماده شد
- [ ] QR Code ایجاد شد
- [ ] پیام سربازان آماده شد
- [ ] کانال توزیع انتخاب شد

### 🚀 آماده استفاده!

پس از تکمیل این مراحل:
- ✅ APK هر 25 دقیقه آماده می‌شود
- ✅ لینک دانلود همیشه به‌روز است
- ✅ سربازان می‌توانند نصب کنند
- ✅ به‌روزرسانی خودکار است

**🎯 پروژه پارتیزان آماده خدمت است!**
