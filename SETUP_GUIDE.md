# 🚀 Partizan Kurulum Rehberi

B<PERSON>ber, Partizan uygulamasını çalıştırmak için gerekli adımları açıklar.

## 📋 Gereksinimler

### Sistem Gereksinimleri
- **Flutter SDK**: 3.10.0 veya üzeri
- **Dart SDK**: 3.0.0 veya üzeri
- **Android Studio**: 2022.1 veya üzeri (Android geliştirme için)
- **Xcode**: 14.0 veya üzeri (iOS geliştirme için)
- **Git**: Versiyon kontrolü için

### Platform Gereksinimleri
- **Android**: API Level 21 (Android 5.0) veya üzeri
- **iOS**: iOS 11.0 veya üzeri

## 🛠️ Kurulum Adımları

### 1. Flutter Kurulumu

#### Windows
```bash
# Flutter SDK'yı indirin
# https://docs.flutter.dev/get-started/install/windows

# PATH'e ekleyin
# C:\flutter\bin

# Kurulumu doğrulayın
flutter doctor
```

#### macOS
```bash
# Homebrew ile kurulum
brew install flutter

# Veya manuel kurulum
# https://docs.flutter.dev/get-started/install/macos

# Kurulumu doğrulayın
flutter doctor
```

#### Linux
```bash
# Snap ile kurulum
sudo snap install flutter --classic

# Veya manuel kurulum
# https://docs.flutter.dev/get-started/install/linux

# Kurulumu doğrulayın
flutter doctor
```

### 2. Proje Kurulumu

```bash
# Projeyi klonlayın
git clone https://github.com/your-username/partizan.git
cd partizan

# Dependencies yükleyin
flutter pub get

# Platform dosyalarını oluşturun
flutter create --platforms android,ios .
```

### 3. API Anahtarları Ayarlama

#### Google Maps API
1. [Google Cloud Console](https://console.cloud.google.com/) açın
2. Yeni proje oluşturun veya mevcut projeyi seçin
3. Maps SDK for Android ve iOS'u etkinleştirin
4. API anahtarı oluşturun

#### Android Ayarları
```xml
<!-- android/app/src/main/AndroidManifest.xml -->
<meta-data 
    android:name="com.google.android.geo.API_KEY"
    android:value="YOUR_GOOGLE_MAPS_API_KEY"/>
```

#### iOS Ayarları
```xml
<!-- ios/Runner/Info.plist -->
<key>GMSApiKey</key>
<string>YOUR_GOOGLE_MAPS_API_KEY</string>
```

### 4. İzinler Ayarlama

#### Android İzinleri
```xml
<!-- android/app/src/main/AndroidManifest.xml -->
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
<uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
```

#### iOS İzinleri
```xml
<!-- ios/Runner/Info.plist -->
<key>NSLocationWhenInUseUsageDescription</key>
<string>Bu uygulama konum takibi için GPS kullanır</string>
<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
<string>Arka plan konum takibi için gerekli</string>
```

## 🏃‍♂️ Uygulamayı Çalıştırma

### Debug Modu
```bash
# Android emülatör veya cihazda
flutter run

# iOS simülatör veya cihazda
flutter run

# Web'de (geliştirme için)
flutter run -d chrome
```

### Release Modu
```bash
# Android APK oluştur
flutter build apk --release

# iOS IPA oluştur
flutter build ios --release

# Web build
flutter build web --release
```

## 🧪 Test Etme

### Unit Testler
```bash
# Tüm testleri çalıştır
flutter test

# Belirli test dosyası
flutter test test/widget_test.dart

# Coverage raporu
flutter test --coverage
```

### Integration Testler
```bash
# Integration testleri çalıştır
flutter drive --target=test_driver/app.dart
```

## 📱 Demo Hesapları

### Komutan Hesabı
- **Kullanıcı Adı**: `komutan`
- **Şifre**: `partizan2024`
- **Özellikler**: 
  - Tüm askerleri görme
  - Komutan paneli erişimi
  - Mesaj gönderme

### Asker Hesabı
- **Kullanıcı Adı**: `asker`
- **Şifre**: `partizan2024`
- **Özellikler**:
  - Konum paylaşma
  - Mesaj alma
  - Acil durum sinyali

## 🔧 Sorun Giderme

### Flutter Doctor Sorunları
```bash
# Flutter doctor çalıştır
flutter doctor

# Eksik bileşenleri yükle
flutter doctor --android-licenses
```

### Build Sorunları
```bash
# Cache temizle
flutter clean
flutter pub get

# Gradle cache temizle (Android)
cd android
./gradlew clean

# Pod cache temizle (iOS)
cd ios
rm -rf Pods
rm Podfile.lock
pod install
```

### Konum İzni Sorunları
- Android: Ayarlar > Uygulamalar > Partizan > İzinler
- iOS: Ayarlar > Gizlilik > Konum Servisleri > Partizan

### API Anahtar Sorunları
- Google Cloud Console'da API anahtarının aktif olduğunu kontrol edin
- Kısıtlamaların doğru ayarlandığını kontrol edin
- Faturalandırmanın aktif olduğunu kontrol edin

## 📊 Performans Optimizasyonu

### Android
```bash
# ProGuard etkinleştir
# android/app/build.gradle
buildTypes {
    release {
        minifyEnabled true
        useProguard true
        proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
    }
}
```

### iOS
```bash
# Bitcode etkinleştir
# ios/Runner.xcodeproj/project.pbxproj
ENABLE_BITCODE = YES;
```

## 🔒 Güvenlik Ayarları

### Network Security
```xml
<!-- android/app/src/main/res/xml/network_security_config.xml -->
<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <domain-config cleartextTrafficPermitted="false">
        <domain includeSubdomains="true">your-api-domain.com</domain>
    </domain-config>
</network-security-config>
```

### Certificate Pinning
```dart
// lib/core/services/api_service.dart
(dio.httpClientAdapter as DefaultHttpClientAdapter).onHttpClientCreate = (client) {
  client.badCertificateCallback = (cert, host, port) {
    // Certificate validation logic
    return false;
  };
  return client;
};
```

## 📞 Destek

### Dokümantasyon
- [Flutter Docs](https://docs.flutter.dev/)
- [Dart Docs](https://dart.dev/guides)
- [Google Maps Flutter](https://pub.dev/packages/google_maps_flutter)

### Topluluk
- [Flutter Community](https://flutter.dev/community)
- [Stack Overflow](https://stackoverflow.com/questions/tagged/flutter)
- [GitHub Issues](https://github.com/your-username/partizan/issues)

### İletişim
- **E-posta**: <EMAIL>
- **Telefon**: +90 XXX XXX XX XX
- **Website**: https://partizan-military.com

---

**✅ Kurulum tamamlandıktan sonra uygulamayı test etmeyi unutmayın!**
