# 📱 Partizan APK Build Rehberi

## 🎯 Hızlı APK Oluşturma

### 1. Flutter <PERSON> (Windows)

```bash
# 1. Flutter SDK indirin
# https://storage.googleapis.com/flutter_infra_release/releases/stable/windows/flutter_windows_3.16.0-stable.zip

# 2. C:\ dizinine çıkarın
# C:\flutter\

# 3. PATH'e ekleyin
# Sistem Özellikleri > Gelişmiş > Ortam Değişkenleri > PATH
# C:\flutter\bin ekleyin

# 4. Yeni terminal açın ve test edin
flutter --version
flutter doctor
```

### 2. Android Studio Kurulumu

```bash
# 1. Android Studio indirin
# https://developer.android.com/studio

# 2. Kurun ve SDK'ları indirin
# Tools > SDK Manager > Android SDK

# 3. Flutter doctor çalı<PERSON>tırın
flutter doctor --android-licenses
```

### 3. APK Build Etme

```bash
# Proje dizinine gidin
cd C:\Users\<USER>\OneDrive\Desktop\partizan-1

# Dependencies yükleyin
flutter pub get

# Android platform dosyalarını oluşturun
flutter create --platforms android .

# APK build edin
flutter build apk --release

# APK dosyası burada olacak:
# build\app\outputs\flutter-apk\app-release.apk
```

## 🌐 Alternatif: Web Versiyonu

Web versiyonu için daha hızlı test edebilirsiniz:

```bash
# Web build
flutter build web --release

# Local server başlatın
cd build\web
python -m http.server 8000

# Tarayıcıda açın: http://localhost:8000
```

## 📲 APK Paylaşım Yöntemleri

### 1. Google Drive
```bash
# APK'yı Google Drive'a yükleyin
# Paylaşım linkini alın
# Askerlere gönderin
```

### 2. Firebase App Distribution
```bash
# Firebase Console > App Distribution
# APK yükleyin
# Test grupları oluşturun
# Davet linkleri gönderin
```

### 3. GitHub Releases
```bash
# GitHub repository > Releases
# New Release oluşturun
# APK dosyasını attach edin
# Download linkini paylaşın
```

## 🔧 Build Sorunları Çözümü

### Gradle Hatası
```bash
cd android
./gradlew clean
cd ..
flutter clean
flutter pub get
flutter build apk
```

### SDK Hatası
```bash
flutter doctor
flutter config --android-sdk C:\Users\<USER>\AppData\Local\Android\Sdk
```

### Signing Hatası
```bash
# android/app/build.gradle
android {
    buildTypes {
        release {
            signingConfig signingConfigs.debug
        }
    }
}
```

## 📋 Minimum Gereksinimler

### Geliştirici (Siz)
- Windows 10/11
- 8GB RAM
- 10GB boş disk alanı
- İnternet bağlantısı

### Son Kullanıcı (Askerler)
- Android 5.0+ (API 21)
- 2GB RAM
- 100MB boş alan
- GPS desteği
- İnternet bağlantısı

## 🚀 Hızlı Test Senaryosu

1. **APK Build**: 30-60 dakika
2. **Google Drive Upload**: 5 dakika  
3. **Asker Download**: 2 dakika
4. **App Install**: 1 dakika
5. **Test**: 10 dakika

**Toplam Süre**: ~1-2 saat

## 📞 Acil Durum Çözümü

Eğer Flutter kurulumu çok uzun sürerse:

### Online Flutter IDE
```bash
# DartPad kullanın (sınırlı)
# https://dartpad.dev

# Replit kullanın
# https://replit.com

# CodePen kullanın
# https://codepen.io
```

### APK Builder Servisleri
```bash
# Codemagic (ücretsiz plan)
# https://codemagic.io

# GitHub Actions (ücretsiz)
# https://github.com/features/actions

# Bitrise (ücretsiz plan)
# https://www.bitrise.io
```

## 🎯 Önerilen Yol

**En hızlı çözüm:**

1. Flutter'ı kurun (45 dakika)
2. APK build edin (15 dakika)
3. Google Drive'a yükleyin (5 dakika)
4. Linkleri paylaşın (1 dakika)

**Toplam**: ~1 saat

Bu şekilde askerler APK'yı indirebilir ve test edebilirsiniz!
