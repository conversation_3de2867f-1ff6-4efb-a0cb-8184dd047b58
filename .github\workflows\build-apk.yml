name: 🚀 Build Partizan APK

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]
  workflow_dispatch:
    inputs:
      version:
        description: 'Version number'
        required: false
        default: '1.0.0'

jobs:
  build-android:
    name: 📱 Build Android APK
    runs-on: ubuntu-latest
    
    steps:
    - name: 📥 Checkout Repository
      uses: actions/checkout@v4
      
    - name: ☕ Setup Java
      uses: actions/setup-java@v4
      with:
        distribution: 'zulu'
        java-version: '17'
        
    - name: 🐦 Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.16.0'
        channel: 'stable'
        cache: true
        
    - name: 📦 Get Dependencies
      run: flutter pub get
      
    - name: 🔍 Analyze Code
      run: flutter analyze
      
    - name: 🧪 Run Tests
      run: flutter test
      
    - name: 🏗️ Build APK (Debug)
      run: flutter build apk --debug
      
    - name: 🏗️ Build APK (Release)
      run: flutter build apk --release --split-per-abi
      
    - name: 📊 APK Info
      run: |
        echo "📱 APK Build Information:"
        echo "========================"
        ls -la build/app/outputs/flutter-apk/
        echo ""
        echo "📏 File Sizes:"
        du -h build/app/outputs/flutter-apk/*.apk
        
    - name: 📤 Upload Debug APK
      uses: actions/upload-artifact@v4
      with:
        name: partizan-debug-apk
        path: build/app/outputs/flutter-apk/app-debug.apk
        retention-days: 30
        
    - name: 📤 Upload Release APKs
      uses: actions/upload-artifact@v4
      with:
        name: partizan-release-apks
        path: build/app/outputs/flutter-apk/app-*-release.apk
        retention-days: 90
        
    - name: 📤 Upload Universal APK
      uses: actions/upload-artifact@v4
      with:
        name: partizan-universal-apk
        path: build/app/outputs/flutter-apk/app-release.apk
        retention-days: 90

  build-web:
    name: 🌐 Build Web Version
    runs-on: ubuntu-latest
    
    steps:
    - name: 📥 Checkout Repository
      uses: actions/checkout@v4
      
    - name: 🐦 Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.16.0'
        channel: 'stable'
        cache: true
        
    - name: 📦 Get Dependencies
      run: flutter pub get
      
    - name: 🌐 Build Web
      run: flutter build web --release --web-renderer html
      
    - name: 📤 Upload Web Build
      uses: actions/upload-artifact@v4
      with:
        name: partizan-web
        path: build/web/
        retention-days: 30

  create-release:
    name: 🎉 Create Release
    needs: [build-android, build-web]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master'
    
    steps:
    - name: 📥 Checkout Repository
      uses: actions/checkout@v4
      
    - name: 📥 Download APKs
      uses: actions/download-artifact@v4
      with:
        name: partizan-release-apks
        path: ./apks/
        
    - name: 📥 Download Universal APK
      uses: actions/download-artifact@v4
      with:
        name: partizan-universal-apk
        path: ./apks/
        
    - name: 📅 Get Date
      id: date
      run: echo "date=$(date +'%Y-%m-%d-%H-%M')" >> $GITHUB_OUTPUT
      
    - name: 🏷️ Create Release
      id: create_release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: v${{ steps.date.outputs.date }}
        release_name: 🎯 Partizan Release v${{ steps.date.outputs.date }}
        body: |
          ## 🚀 Partizan - Asker Takip Uygulaması
          
          ### 📱 دانلود برای سربازان:
          
          **🎯 APK اصلی (توصیه شده):**
          - `app-release.apk` - برای همه گوشی‌ها
          
          **📱 APK بهینه شده:**
          - `app-arm64-v8a-release.apk` - گوشی‌های جدید (64-bit)
          - `app-armeabi-v7a-release.apk` - گوشی‌های قدیمی (32-bit)
          - `app-x86_64-release.apk` - شبیه‌ساز و تبلت‌ها
          
          ### 🔐 حساب‌های تست:
          - **سرباز**: `asker` / `partizan2024`
          - **فرمانده**: `komutan` / `partizan2024`
          
          ### 📋 راهنمای نصب:
          1. APK را دانلود کنید
          2. "منابع نامعلوم" را فعال کنید
          3. فایل را نصب کنید
          4. مجوزها را بدهید
          5. وارد شوید
          
          ### ✨ ویژگی‌های جدید:
          - 🔐 سیستم امنیت پیشرفته
          - 📍 ردیابی GPS دقیق
          - 💬 پیام‌رسانی امن
          - 🗺️ نقشه‌های آنلاین/آفلاین
          - 🚨 سیگنال اضطراری
          
          ### 🛠️ پشتیبانی:
          - 📧 <EMAIL>
          - 📱 راهنمای کامل در repository
        draft: false
        prerelease: false
        
    - name: 📤 Upload Universal APK to Release
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ steps.create_release.outputs.upload_url }}
        asset_path: ./apks/app-release.apk
        asset_name: partizan-universal.apk
        asset_content_type: application/vnd.android.package-archive
        
    - name: 📤 Upload ARM64 APK to Release
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ steps.create_release.outputs.upload_url }}
        asset_path: ./apks/app-arm64-v8a-release.apk
        asset_name: partizan-arm64.apk
        asset_content_type: application/vnd.android.package-archive
        
    - name: 📤 Upload ARM APK to Release
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ steps.create_release.outputs.upload_url }}
        asset_path: ./apks/app-armeabi-v7a-release.apk
        asset_name: partizan-arm.apk
        asset_content_type: application/vnd.android.package-archive

  notify-completion:
    name: 📢 Notify Build Completion
    needs: [build-android, build-web, create-release]
    runs-on: ubuntu-latest
    if: always()
    
    steps:
    - name: 📊 Build Summary
      run: |
        echo "🎯 PARTIZAN BUILD SUMMARY"
        echo "========================"
        echo "📱 Android Build: ${{ needs.build-android.result }}"
        echo "🌐 Web Build: ${{ needs.build-web.result }}"
        echo "🎉 Release: ${{ needs.create-release.result }}"
        echo ""
        echo "📥 Download Links:"
        echo "- APK Files: GitHub Actions > Artifacts"
        echo "- Release: GitHub > Releases > Latest"
        echo ""
        echo "🔐 Test Accounts:"
        echo "- Soldier: asker/partizan2024"
        echo "- Commander: komutan/partizan2024"
