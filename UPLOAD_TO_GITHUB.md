# 📤 راهنمای آپلود به GitHub (5 دقیقه)

## 🎯 مرحله 1: ایجاد Repository

### 1.1 ورود به GitHub
```
1. به GitHub.com بروید
2. با حساب hakan123456h وارد شوید
3. "+" کلیک کنید > "New repository"
```

### 1.2 تنظیمات Repository
```
Repository name: partizan-military-app
Description: 🎯 Partizan - Real-time Military Tracking Application
✅ Public (تا همه بتوانند دانلود کنند)
✅ Add a README file
Create repository کلیک کنید
```

## 📁 مرحله 2: آپلود فایل‌ها

### 2.1 آپلود همه فایل‌ها
```
1. Repository صفحه > "uploading an existing file"
2. تمام فایل‌های پروژه را انتخاب کنید:

📁 فایل‌های ضروری:
├── .github/workflows/build-apk.yml
├── lib/ (تمام فایل‌ها)
├── android/ (تمام فایل‌ها)  
├── ios/ (تمام فایل‌ها)
├── test/
├── pubspec.yaml
├── README.md
├── .gitignore
└── تمام فایل‌های .md

3. Drag & Drop همه فایل‌ها
4. Commit message: "🎯 Complete Partizan military app"
5. "Commit changes" کلیک کنید
```

### 2.2 بررسی آپلود
```
✅ چک کنید:
- .github/workflows/build-apk.yml موجود است
- lib/ پوشه کامل است
- android/ پوشه کامل است
- pubspec.yaml موجود است
```

## ⚡ مرحله 3: فعال‌سازی Actions

### 3.1 Actions Tab
```
1. Repository > "Actions" tab
2. "I understand my workflows, go ahead and enable them"
3. GitHub Actions فعال می‌شود
```

### 3.2 اجرای اولین Build
```
1. Actions > "🚀 Build Partizan APK"
2. "Run workflow" کلیک کنید
3. Branch: main
4. "Run workflow" کلیک کنید
```

## ⏱️ مرحله 4: منتظر Build (25 دقیقه)

### 4.1 نظارت بر Build
```
📊 مراحل Build:
├── 📥 Checkout Repository (30s)
├── ☕ Setup Java (1m)
├── 🐦 Setup Flutter (3m)
├── 📦 Get Dependencies (2m)
├── 🔍 Analyze Code (1m)
├── 🧪 Run Tests (2m)
├── 🏗️ Build APK Debug (5m)
├── 🏗️ Build APK Release (8m)
├── 🌐 Build Web (3m)
└── 🎉 Create Release (2m)

⏱️ کل زمان: ~25 دقیقه
```

### 4.2 بررسی وضعیت
```
✅ موفق: سبز با تیک
❌ ناموفق: قرمز با X
🟡 در حال اجرا: زرد با دایره
```

## 📱 مرحله 5: دریافت لینک دانلود

### 5.1 از Releases (بهترین)
```
پس از build موفق:

1. Repository > "Releases" tab
2. آخرین release کلیک کنید
3. Assets section:
   - partizan-universal.apk
   - partizan-arm64.apk  
   - partizan-arm.apk

4. Right-click > "Copy link address"
```

### 5.2 لینک نهایی
```
فرمت لینک:
https://github.com/hakan123456h/partizan-military-app/releases/download/v2024-XX-XX-XX-XX/partizan-universal.apk

مثال:
https://github.com/hakan123456h/partizan-military-app/releases/download/v2024-01-15-14-30/partizan-universal.apk
```

## 📲 مرحله 6: پیام برای سربازان

### 6.1 پیام نمونه
```
🎯 دانلود اپلیکیشن پارتیزان

📱 لینک دانلود:
[LINK_WILL_BE_HERE]

📏 حجم: ~50MB
⏱️ زمان دانلود: 2-5 دقیقه

🔐 حساب تست:
👤 نام کاربری: asker
🔑 رمز عبور: partizan2024

📋 مراحل نصب:
1️⃣ لینک را باز کنید
2️⃣ "Download" کلیک کنید
3️⃣ منتظر دانلود بمانید
4️⃣ فایل را باز کنید
5️⃣ "منابع نامعلوم" فعال کنید
6️⃣ "نصب" کلیک کنید
7️⃣ مجوزها را بدهید
8️⃣ وارد شوید

⚠️ مهم:
- فقط از لینک رسمی دانلود کنید
- مجوز موقعیت مکانی بدهید
- گوشی را شارژ نگه دارید

📞 پشتیبانی:
در صورت مشکل پیام دهید
```

### 6.2 QR Code
```
پس از دریافت لینک:

1. qr-code-generator.com بروید
2. لینک APK را paste کنید
3. QR Code generate کنید
4. عکس QR Code را save کنید
5. همراه پیام ارسال کنید
```

## 🔄 مرحله 7: به‌روزرسانی خودکار

### 7.1 Auto-Update
```
هر تغییر در کد:
├── ✅ خودکار build می‌شود
├── ✅ APK جدید تولید می‌شود  
├── ✅ Release جدید ایجاد می‌شود
└── ✅ لینک به‌روز می‌شود
```

### 7.2 اطلاع‌رسانی
```
برای اطلاع از نسخه جدید:
1. Repository > Watch > All Activity
2. ایمیل notification دریافت کنید
3. سربازان را مطلع کنید
```

---

## ⚡ خلاصه سریع:

```bash
1. GitHub.com > New Repository (2 دقیقه)
2. آپلود فایل‌ها (3 دقیقه)
3. Actions > Run workflow (1 دقیقه)
4. منتظر build (25 دقیقه)
5. Releases > Copy link (1 دقیقه)
6. ارسال به سربازان (2 دقیقه)

کل زمان: 35 دقیقه
```

**🚀 پس از تکمیل این مراحل، لینک دانلود آماده خواهد بود!**
