name: partizan
description: "Partizan - Asker Takip Uygulaması | Live Military Tracking Application"
version: 1.0.0+1

environment:
  sdk: ">=3.0.0 <4.0.0"
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # Maps & Location
  google_maps_flutter: ^2.5.0
  mapbox_gl: ^0.16.0
  geolocator: ^10.1.0
  location: ^5.0.3
  geocoding: ^2.1.1

  # State Management
  provider: ^6.1.1
  riverpod: ^2.4.9
  flutter_riverpod: ^2.4.9

  # Network & API
  http: ^1.1.0
  dio: ^5.3.2
  retrofit: ^4.0.3
  json_annotation: ^4.8.1

  # Security & Encryption
  encrypt: ^5.0.1
  crypto: ^3.0.3
  flutter_secure_storage: ^9.0.0

  # Real-time Communication
  socket_io_client: ^2.0.3+1
  web_socket_channel: ^2.4.0

  # UI & Theming
  cupertino_icons: ^1.0.6
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.0
  shimmer: ^3.0.0
  lottie: ^2.7.0

  # Utilities
  shared_preferences: ^2.2.2
  path_provider: ^2.1.1
  permission_handler: ^11.0.1
  device_info_plus: ^9.1.0
  package_info_plus: ^4.2.0
  connectivity_plus: ^5.0.1

  # Date & Time
  intl: ^0.18.1
  timeago: ^3.6.0

  # Logging & Analytics
  logger: ^2.0.2+1
  firebase_core: ^2.24.2
  firebase_analytics: ^10.7.4
  firebase_crashlytics: ^3.4.8

  # Background Services
  workmanager: ^0.5.2
  flutter_background_service: ^5.0.5

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1
  build_runner: ^2.4.7
  retrofit_generator: ^8.0.4
  json_serializable: ^6.7.1

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/icons/
    - assets/fonts/
    - assets/animations/

  fonts:
    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto-Regular.ttf
        - asset: assets/fonts/Roboto-Bold.ttf
          weight: 700
    - family: Military
      fonts:
        - asset: assets/fonts/Military-Regular.ttf