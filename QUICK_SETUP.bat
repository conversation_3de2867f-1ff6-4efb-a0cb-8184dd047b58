@echo off
echo ========================================
echo    PARTIZAN APP - QUICK SETUP
echo ========================================
echo.

echo [1/5] Creating directories...
mkdir C:\flutter 2>nul
mkdir C:\android-sdk 2>nul

echo [2/5] Downloading Flutter SDK...
echo Please download Flutter from: https://storage.googleapis.com/flutter_infra_release/releases/stable/windows/flutter_windows_3.16.0-stable.zip
echo Extract to: C:\flutter\
echo.
pause

echo [3/5] Setting up PATH...
echo Please add to your PATH environment variable:
echo C:\flutter\bin
echo.
echo Instructions:
echo 1. Press Win + R, type "sysdm.cpl"
echo 2. Go to Advanced tab
echo 3. Click Environment Variables
echo 4. Edit PATH variable
echo 5. Add: C:\flutter\bin
echo.
pause

echo [4/5] Testing Flutter installation...
C:\flutter\bin\flutter.bat --version
if %errorlevel% neq 0 (
    echo ERROR: Flutter not found in PATH
    echo Please restart command prompt after setting PATH
    pause
    exit /b 1
)

echo [5/5] Building APK...
cd /d "%~dp0"
C:\flutter\bin\flutter.bat pub get
C:\flutter\bin\flutter.bat create --platforms android .
C:\flutter\bin\flutter.bat build apk --release

echo.
echo ========================================
echo    BUILD COMPLETED!
echo ========================================
echo.
echo APK Location: build\app\outputs\flutter-apk\app-release.apk
echo File Size: ~50MB
echo.
echo Next Steps:
echo 1. Upload APK to Google Drive
echo 2. Share download link with soldiers
echo 3. Send installation guide
echo.
pause
