import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import '../constants/app_constants.dart';
import 'security_service.dart';

class ApiService {
  static ApiService? _instance;
  static ApiService get instance => _instance ??= ApiService._();
  ApiService._();

  late final Dio _dio;
  final Connectivity _connectivity = Connectivity();

  /// API servisini başlat
  Future<void> initialize() async {
    _dio = Dio(BaseOptions(
      baseUrl: AppConstants.baseUrl,
      connectTimeout: const Duration(milliseconds: AppConstants.connectionTimeout),
      receiveTimeout: const Duration(milliseconds: AppConstants.receiveTimeout),
      sendTimeout: const Duration(milliseconds: AppConstants.sendTimeout),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': 'Partizan/${AppConstants.appVersion}',
      },
    ));

    // Interceptor'ları ekle
    _dio.interceptors.add(_AuthInterceptor());
    _dio.interceptors.add(_LoggingInterceptor());
    _dio.interceptors.add(_ErrorInterceptor());
  }

  /// İnternet bağlantısını kontrol et
  Future<bool> hasInternetConnection() async {
    try {
      final connectivityResult = await _connectivity.checkConnectivity();
      if (connectivityResult == ConnectivityResult.none) {
        return false;
      }
      
      // Gerçek bağlantı testi
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// Kullanıcı girişi
  Future<ApiResponse<LoginResponse>> login(LoginRequest request) async {
    try {
      final response = await _dio.post(
        '${AppConstants.authEndpoint}/login',
        data: request.toJson(),
      );
      
      return ApiResponse.success(
        LoginResponse.fromJson(response.data),
      );
    } catch (e) {
      return ApiResponse.error(_handleError(e));
    }
  }

  /// Kullanıcı çıkışı
  Future<ApiResponse<void>> logout() async {
    try {
      await _dio.post('${AppConstants.authEndpoint}/logout');
      return ApiResponse.success(null);
    } catch (e) {
      return ApiResponse.error(_handleError(e));
    }
  }

  /// Token yenile
  Future<ApiResponse<TokenResponse>> refreshToken(String refreshToken) async {
    try {
      final response = await _dio.post(
        '${AppConstants.authEndpoint}/refresh',
        data: {'refresh_token': refreshToken},
      );
      
      return ApiResponse.success(
        TokenResponse.fromJson(response.data),
      );
    } catch (e) {
      return ApiResponse.error(_handleError(e));
    }
  }

  /// Konum güncelle
  Future<ApiResponse<void>> updateLocation(LocationUpdateRequest request) async {
    try {
      await _dio.post(
        '${AppConstants.trackingEndpoint}/location',
        data: request.toJson(),
      );
      
      return ApiResponse.success(null);
    } catch (e) {
      return ApiResponse.error(_handleError(e));
    }
  }

  /// Konum geçmişi al
  Future<ApiResponse<List<LocationData>>> getLocationHistory(
    String userId, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'user_id': userId,
        if (startDate != null) 'start_date': startDate.toIso8601String(),
        if (endDate != null) 'end_date': endDate.toIso8601String(),
      };

      final response = await _dio.get(
        '${AppConstants.trackingEndpoint}/history',
        queryParameters: queryParams,
      );

      final List<dynamic> data = response.data['locations'];
      final locations = data.map((json) => LocationData.fromJson(json)).toList();
      
      return ApiResponse.success(locations);
    } catch (e) {
      return ApiResponse.error(_handleError(e));
    }
  }

  /// Tüm askerlerin konumlarını al (komutan için)
  Future<ApiResponse<List<SoldierLocationData>>> getAllSoldierLocations() async {
    try {
      final response = await _dio.get(
        '${AppConstants.commanderEndpoint}/soldiers/locations',
      );

      final List<dynamic> data = response.data['soldiers'];
      final soldiers = data.map((json) => SoldierLocationData.fromJson(json)).toList();
      
      return ApiResponse.success(soldiers);
    } catch (e) {
      return ApiResponse.error(_handleError(e));
    }
  }

  /// Mesaj gönder
  Future<ApiResponse<void>> sendMessage(MessageRequest request) async {
    try {
      await _dio.post(
        '${AppConstants.messagingEndpoint}/send',
        data: request.toJson(),
      );
      
      return ApiResponse.success(null);
    } catch (e) {
      return ApiResponse.error(_handleError(e));
    }
  }

  /// Mesajları al
  Future<ApiResponse<List<MessageData>>> getMessages({
    String? conversationId,
    int limit = 50,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'limit': limit,
        if (conversationId != null) 'conversation_id': conversationId,
      };

      final response = await _dio.get(
        '${AppConstants.messagingEndpoint}/messages',
        queryParameters: queryParams,
      );

      final List<dynamic> data = response.data['messages'];
      final messages = data.map((json) => MessageData.fromJson(json)).toList();
      
      return ApiResponse.success(messages);
    } catch (e) {
      return ApiResponse.error(_handleError(e));
    }
  }

  /// Acil durum sinyali gönder
  Future<ApiResponse<void>> sendEmergencySignal(EmergencyRequest request) async {
    try {
      await _dio.post(
        '${AppConstants.trackingEndpoint}/emergency',
        data: request.toJson(),
      );
      
      return ApiResponse.success(null);
    } catch (e) {
      return ApiResponse.error(_handleError(e));
    }
  }

  /// Hata işleyici
  String _handleError(dynamic error) {
    if (error is DioException) {
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          return 'Bağlantı zaman aşımı';
        case DioExceptionType.badResponse:
          final statusCode = error.response?.statusCode;
          final message = error.response?.data?['message'] ?? 'Sunucu hatası';
          return 'HTTP $statusCode: $message';
        case DioExceptionType.cancel:
          return 'İstek iptal edildi';
        case DioExceptionType.connectionError:
          return 'Bağlantı hatası';
        default:
          return 'Bilinmeyen hata: ${error.message}';
      }
    }
    return error.toString();
  }
}

/// Auth Interceptor
class _AuthInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    // Token ekle
    final token = await SecurityService.instance.getAuthToken();
    if (token != null) {
      options.headers['Authorization'] = 'Bearer $token';
    }

    // Device ID ekle
    final deviceId = await SecurityService.instance.getDeviceId();
    if (deviceId != null) {
      options.headers['X-Device-ID'] = deviceId;
    }

    handler.next(options);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    // 401 hatası durumunda token yenile
    if (err.response?.statusCode == 401) {
      try {
        // Token yenileme işlemi burada yapılacak
        // Şimdilik sadece hata döndür
        handler.next(err);
      } catch (e) {
        handler.next(err);
      }
    } else {
      handler.next(err);
    }
  }
}

/// Logging Interceptor
class _LoggingInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    print('🚀 REQUEST: ${options.method} ${options.uri}');
    print('📤 DATA: ${options.data}');
    handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    print('✅ RESPONSE: ${response.statusCode} ${response.requestOptions.uri}');
    print('📥 DATA: ${response.data}');
    handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    print('❌ ERROR: ${err.response?.statusCode} ${err.requestOptions.uri}');
    print('💥 MESSAGE: ${err.message}');
    handler.next(err);
  }
}

/// Error Interceptor
class _ErrorInterceptor extends Interceptor {
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    // Hata loglama ve raporlama
    // Firebase Crashlytics entegrasyonu burada yapılacak
    handler.next(err);
  }
}

/// API Response wrapper
class ApiResponse<T> {
  final bool success;
  final T? data;
  final String? error;

  ApiResponse.success(this.data) : success = true, error = null;
  ApiResponse.error(this.error) : success = false, data = null;
}

/// Data modelleri
class LoginRequest {
  final String username;
  final String password;
  final String? deviceId;

  LoginRequest({
    required this.username,
    required this.password,
    this.deviceId,
  });

  Map<String, dynamic> toJson() => {
    'username': username,
    'password': password,
    if (deviceId != null) 'device_id': deviceId,
  };
}

class LoginResponse {
  final String token;
  final String refreshToken;
  final UserData user;

  LoginResponse({
    required this.token,
    required this.refreshToken,
    required this.user,
  });

  factory LoginResponse.fromJson(Map<String, dynamic> json) => LoginResponse(
    token: json['token'],
    refreshToken: json['refresh_token'],
    user: UserData.fromJson(json['user']),
  );
}

class TokenResponse {
  final String token;
  final String refreshToken;

  TokenResponse({
    required this.token,
    required this.refreshToken,
  });

  factory TokenResponse.fromJson(Map<String, dynamic> json) => TokenResponse(
    token: json['token'],
    refreshToken: json['refresh_token'],
  );
}

class UserData {
  final String id;
  final String username;
  final String role;
  final String? name;

  UserData({
    required this.id,
    required this.username,
    required this.role,
    this.name,
  });

  factory UserData.fromJson(Map<String, dynamic> json) => UserData(
    id: json['id'],
    username: json['username'],
    role: json['role'],
    name: json['name'],
  );
}

class LocationUpdateRequest {
  final double latitude;
  final double longitude;
  final double accuracy;
  final DateTime timestamp;
  final Map<String, dynamic>? metadata;

  LocationUpdateRequest({
    required this.latitude,
    required this.longitude,
    required this.accuracy,
    required this.timestamp,
    this.metadata,
  });

  Map<String, dynamic> toJson() => {
    'latitude': latitude,
    'longitude': longitude,
    'accuracy': accuracy,
    'timestamp': timestamp.toIso8601String(),
    if (metadata != null) 'metadata': metadata,
  };
}

class LocationData {
  final double latitude;
  final double longitude;
  final double accuracy;
  final DateTime timestamp;

  LocationData({
    required this.latitude,
    required this.longitude,
    required this.accuracy,
    required this.timestamp,
  });

  factory LocationData.fromJson(Map<String, dynamic> json) => LocationData(
    latitude: json['latitude'].toDouble(),
    longitude: json['longitude'].toDouble(),
    accuracy: json['accuracy'].toDouble(),
    timestamp: DateTime.parse(json['timestamp']),
  );
}

class SoldierLocationData {
  final String userId;
  final String name;
  final String role;
  final LocationData location;
  final String status;

  SoldierLocationData({
    required this.userId,
    required this.name,
    required this.role,
    required this.location,
    required this.status,
  });

  factory SoldierLocationData.fromJson(Map<String, dynamic> json) => SoldierLocationData(
    userId: json['user_id'],
    name: json['name'],
    role: json['role'],
    location: LocationData.fromJson(json['location']),
    status: json['status'],
  );
}

class MessageRequest {
  final String recipientId;
  final String content;
  final String type;

  MessageRequest({
    required this.recipientId,
    required this.content,
    required this.type,
  });

  Map<String, dynamic> toJson() => {
    'recipient_id': recipientId,
    'content': content,
    'type': type,
  };
}

class MessageData {
  final String id;
  final String senderId;
  final String recipientId;
  final String content;
  final String type;
  final DateTime timestamp;
  final bool isRead;

  MessageData({
    required this.id,
    required this.senderId,
    required this.recipientId,
    required this.content,
    required this.type,
    required this.timestamp,
    required this.isRead,
  });

  factory MessageData.fromJson(Map<String, dynamic> json) => MessageData(
    id: json['id'],
    senderId: json['sender_id'],
    recipientId: json['recipient_id'],
    content: json['content'],
    type: json['type'],
    timestamp: DateTime.parse(json['timestamp']),
    isRead: json['is_read'] ?? false,
  );
}

class EmergencyRequest {
  final double latitude;
  final double longitude;
  final String message;
  final String type;

  EmergencyRequest({
    required this.latitude,
    required this.longitude,
    required this.message,
    required this.type,
  });

  Map<String, dynamic> toJson() => {
    'latitude': latitude,
    'longitude': longitude,
    'message': message,
    'type': type,
    'timestamp': DateTime.now().toIso8601String(),
  };
}
