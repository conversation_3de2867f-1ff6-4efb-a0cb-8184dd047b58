import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';

import '../../../../core/constants/app_constants.dart';
import '../../../../core/services/location_service.dart';
import '../../../../core/services/security_service.dart';
import '../../../../shared/themes/app_theme.dart';
import '../../../../shared/widgets/custom_button.dart';
import '../../../auth/presentation/pages/login_page.dart';
import '../../../commander/presentation/pages/commander_dashboard.dart';

class TrackingPage extends StatefulWidget {
  const TrackingPage({super.key});

  @override
  State<TrackingPage> createState() => _TrackingPageState();
}

class _TrackingPageState extends State<TrackingPage>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _statusController;
  late Animation<double> _pulseAnimation;
  late Animation<Color?> _statusColorAnimation;
  
  String? _userRole;
  String? _userId;
  bool _isTracking = false;
  bool _isLocationEnabled = false;
  String _statusText = 'Hazırlanıyor...';
  String _locationText = 'Konum alınıyor...';
  
  StreamSubscription? _locationSubscription;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadUserData();
    _checkLocationStatus();
  }

  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _statusController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _statusColorAnimation = ColorTween(
      begin: AppTheme.warningColor,
      end: AppTheme.successColor,
    ).animate(_statusController);

    _pulseController.repeat(reverse: true);
  }

  Future<void> _loadUserData() async {
    final role = await SecurityService.instance.getUserRole();
    final id = await SecurityService.instance.getUserId();
    
    setState(() {
      _userRole = role;
      _userId = id;
    });
  }

  Future<void> _checkLocationStatus() async {
    try {
      final isEnabled = await LocationService.instance.isLocationServiceEnabled();
      setState(() {
        _isLocationEnabled = isEnabled;
        _statusText = isEnabled ? 'Konum servisi aktif' : 'Konum servisi kapalı';
      });
      
      if (isEnabled) {
        _statusController.forward();
      }
    } catch (e) {
      setState(() {
        _statusText = 'Konum kontrolü başarısız';
      });
    }
  }

  Future<void> _startTracking() async {
    try {
      setState(() {
        _statusText = 'Takip başlatılıyor...';
      });

      await LocationService.instance.startTracking();
      
      _locationSubscription = LocationService.instance.listenToLocationUpdates(
        (position) {
          setState(() {
            _locationText = 'Lat: ${position.latitude.toStringAsFixed(6)}\n'
                          'Lng: ${position.longitude.toStringAsFixed(6)}\n'
                          'Doğruluk: ${position.accuracy.toStringAsFixed(1)}m';
          });
        },
        (error) {
          setState(() {
            _statusText = 'Konum hatası: ${error.message}';
          });
        },
      );

      setState(() {
        _isTracking = true;
        _statusText = 'Takip aktif';
      });
      
      _statusController.forward();
      HapticFeedback.lightImpact();
      
    } catch (e) {
      setState(() {
        _statusText = 'Takip başlatılamadı: $e';
      });
      _statusController.reverse();
    }
  }

  Future<void> _stopTracking() async {
    try {
      await LocationService.instance.stopTracking();
      await _locationSubscription?.cancel();
      
      setState(() {
        _isTracking = false;
        _statusText = 'Takip durduruldu';
        _locationText = 'Konum takibi kapalı';
      });
      
      _statusController.reverse();
      HapticFeedback.mediumImpact();
      
    } catch (e) {
      setState(() {
        _statusText = 'Takip durdurulamadı: $e';
      });
    }
  }

  Future<void> _logout() async {
    try {
      // Takibi durdur
      if (_isTracking) {
        await _stopTracking();
      }
      
      // Güvenlik verilerini temizle
      await SecurityService.instance.clearSecurityData();
      
      // Login sayfasına yönlendir
      if (mounted) {
        Navigator.of(context).pushAndRemoveUntil(
          PageRouteBuilder(
            pageBuilder: (context, animation, secondaryAnimation) => 
                const LoginPage(),
            transitionsBuilder: (context, animation, secondaryAnimation, child) {
              return FadeTransition(opacity: animation, child: child);
            },
            transitionDuration: const Duration(milliseconds: 500),
          ),
          (route) => false,
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Çıkış yapılırken hata: $e'),
          backgroundColor: AppTheme.errorColor,
        ),
      );
    }
  }

  void _openCommanderDashboard() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const CommanderDashboard(),
      ),
    );
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _statusController.dispose();
    _locationSubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          _userRole == AppConstants.commanderRole ? 'KOMUTAN PANELİ' : 'ASKER TAKİP',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            letterSpacing: 1.5,
          ),
        ),
        actions: [
          if (_userRole == AppConstants.commanderRole)
            IconButton(
              icon: const Icon(Icons.dashboard),
              onPressed: _openCommanderDashboard,
              tooltip: 'Komutan Paneli',
            ),
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: _logout,
            tooltip: 'Çıkış',
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.primaryGradient,
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.largeSpacing),
            child: Column(
              children: [
                // Kullanıcı bilgileri
                _buildUserInfoCard(),
                
                const SizedBox(height: AppConstants.largeSpacing),
                
                // Durum kartı
                _buildStatusCard(),
                
                const SizedBox(height: AppConstants.largeSpacing),
                
                // Konum bilgileri
                _buildLocationCard(),
                
                const Spacer(),
                
                // Kontrol butonları
                _buildControlButtons(),
                
                const SizedBox(height: AppConstants.mediumSpacing),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildUserInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.mediumSpacing),
        child: Row(
          children: [
            CircleAvatar(
              radius: 30,
              backgroundColor: AppTheme.accentColor,
              child: Icon(
                _userRole == AppConstants.commanderRole 
                    ? Icons.star 
                    : Icons.person,
                size: 30,
                color: Colors.white,
              ),
            ),
            const SizedBox(width: AppConstants.mediumSpacing),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _userRole == AppConstants.commanderRole ? 'KOMUTAN' : 'ASKER',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.accentColor,
                    ),
                  ),
                  Text(
                    'ID: ${_userId ?? 'Yükleniyor...'}',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                ],
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppConstants.smallSpacing,
                vertical: 4,
              ),
              decoration: BoxDecoration(
                color: _isTracking ? AppTheme.successColor : AppTheme.warningColor,
                borderRadius: BorderRadius.circular(AppConstants.smallRadius),
              ),
              child: Text(
                _isTracking ? 'AKTİF' : 'PASİF',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: AppConstants.smallTextSize,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard() {
    return AnimatedBuilder(
      animation: _statusColorAnimation,
      builder: (context, child) {
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.mediumSpacing),
            child: Column(
              children: [
                Row(
                  children: [
                    AnimatedBuilder(
                      animation: _pulseAnimation,
                      builder: (context, child) {
                        return Transform.scale(
                          scale: _isTracking ? _pulseAnimation.value : 1.0,
                          child: Container(
                            width: 20,
                            height: 20,
                            decoration: BoxDecoration(
                              color: _statusColorAnimation.value ?? AppTheme.warningColor,
                              shape: BoxShape.circle,
                            ),
                          ),
                        );
                      },
                    ),
                    const SizedBox(width: AppConstants.mediumSpacing),
                    Expanded(
                      child: Text(
                        'DURUM',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppConstants.smallSpacing),
                Text(
                  _statusText,
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildLocationCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.mediumSpacing),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.location_on,
                  color: AppTheme.accentColor,
                ),
                const SizedBox(width: AppConstants.smallSpacing),
                Text(
                  'KONUM BİLGİLERİ',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.mediumSpacing),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(AppConstants.mediumSpacing),
              decoration: BoxDecoration(
                color: AppTheme.surfaceColor,
                borderRadius: BorderRadius.circular(AppConstants.smallRadius),
              ),
              child: Text(
                _locationText,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontFamily: 'monospace',
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildControlButtons() {
    return Column(
      children: [
        if (!_isTracking)
          PrimaryButton(
            text: 'TAKİBİ BAŞLAT',
            icon: Icons.play_arrow,
            onPressed: _isLocationEnabled ? _startTracking : null,
            width: double.infinity,
          )
        else
          DangerButton(
            text: 'TAKİBİ DURDUR',
            icon: Icons.stop,
            onPressed: _stopTracking,
            width: double.infinity,
          ),
        
        const SizedBox(height: AppConstants.mediumSpacing),
        
        Row(
          children: [
            Expanded(
              child: SecondaryButton(
                text: 'KONUM AYARLARI',
                icon: Icons.settings,
                onPressed: () async {
                  await LocationService.instance.openLocationSettings();
                  _checkLocationStatus();
                },
              ),
            ),
            const SizedBox(width: AppConstants.mediumSpacing),
            Expanded(
              child: SecondaryButton(
                text: 'ACİL DURUM',
                icon: Icons.emergency,
                onPressed: () {
                  // Acil durum fonksiyonu
                  HapticFeedback.heavyImpact();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Acil durum sinyali gönderildi!'),
                      backgroundColor: AppTheme.errorColor,
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ],
    );
  }
}
