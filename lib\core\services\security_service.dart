import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../constants/app_constants.dart';

class SecurityService {
  static SecurityService? _instance;
  static SecurityService get instance => _instance ??= SecurityService._();
  SecurityService._();

  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: IOSAccessibility.first_unlock_this_device,
    ),
  );

  late final Encrypter _encrypter;
  late final IV _iv;
  
  // Güvenlik anahtarları
  static const String _masterKeyName = 'master_key';
  static const String _deviceIdKeyName = 'device_id';
  static const String _sessionTokenKeyName = 'session_token';
  
  /// Güvenlik servisini başlat
  static Future<void> initialize() async {
    await instance._initializeEncryption();
    await instance._generateDeviceId();
  }
  
  /// Şifreleme sistemini başlat
  Future<void> _initializeEncryption() async {
    try {
      // Master key'i al veya oluştur
      String? masterKey = await _secureStorage.read(key: _masterKeyName);
      if (masterKey == null) {
        masterKey = _generateSecureKey();
        await _secureStorage.write(key: _masterKeyName, value: masterKey);
      }
      
      // Encrypter'ı başlat
      final key = Key.fromBase64(masterKey);
      _encrypter = Encrypter(AES(key));
      _iv = IV.fromSecureRandom(16);
      
    } catch (e) {
      throw SecurityException('Şifreleme sistemi başlatılamadı: $e');
    }
  }
  
  /// Güvenli anahtar oluştur
  String _generateSecureKey() {
    final random = Random.secure();
    final bytes = List<int>.generate(32, (i) => random.nextInt(256));
    return base64.encode(bytes);
  }
  
  /// Cihaz ID'si oluştur
  Future<void> _generateDeviceId() async {
    String? deviceId = await _secureStorage.read(key: _deviceIdKeyName);
    if (deviceId == null) {
      deviceId = _generateUniqueDeviceId();
      await _secureStorage.write(key: _deviceIdKeyName, value: deviceId);
    }
  }
  
  /// Benzersiz cihaz ID'si oluştur
  String _generateUniqueDeviceId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = Random.secure();
    final randomBytes = List<int>.generate(16, (i) => random.nextInt(256));
    final combined = '$timestamp${base64.encode(randomBytes)}';
    return sha256.convert(utf8.encode(combined)).toString();
  }
  
  /// Veriyi şifrele
  String encryptData(String data) {
    try {
      final encrypted = _encrypter.encrypt(data, iv: _iv);
      return encrypted.base64;
    } catch (e) {
      throw SecurityException('Veri şifrelenemedi: $e');
    }
  }
  
  /// Veriyi çöz
  String decryptData(String encryptedData) {
    try {
      final encrypted = Encrypted.fromBase64(encryptedData);
      return _encrypter.decrypt(encrypted, iv: _iv);
    } catch (e) {
      throw SecurityException('Veri çözülemedi: $e');
    }
  }
  
  /// Güvenli token oluştur
  String generateSecureToken() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = Random.secure();
    final randomBytes = List<int>.generate(32, (i) => random.nextInt(256));
    final tokenData = '$timestamp${base64.encode(randomBytes)}';
    return sha256.convert(utf8.encode(tokenData)).toString();
  }
  
  /// Şifreyi hash'le
  String hashPassword(String password, String salt) {
    final combined = password + salt + AppConstants.encryptionKey;
    return sha256.convert(utf8.encode(combined)).toString();
  }
  
  /// Salt oluştur
  String generateSalt() {
    final random = Random.secure();
    final bytes = List<int>.generate(16, (i) => random.nextInt(256));
    return base64.encode(bytes);
  }
  
  /// Güvenli veri kaydet
  Future<void> saveSecureData(String key, String value) async {
    try {
      final encryptedValue = encryptData(value);
      await _secureStorage.write(key: key, value: encryptedValue);
    } catch (e) {
      throw SecurityException('Güvenli veri kaydedilemedi: $e');
    }
  }
  
  /// Güvenli veri oku
  Future<String?> readSecureData(String key) async {
    try {
      final encryptedValue = await _secureStorage.read(key: key);
      if (encryptedValue == null) return null;
      return decryptData(encryptedValue);
    } catch (e) {
      throw SecurityException('Güvenli veri okunamadı: $e');
    }
  }
  
  /// Güvenli veri sil
  Future<void> deleteSecureData(String key) async {
    try {
      await _secureStorage.delete(key: key);
    } catch (e) {
      throw SecurityException('Güvenli veri silinemedi: $e');
    }
  }
  
  /// Tüm güvenli verileri sil
  Future<void> clearAllSecureData() async {
    try {
      await _secureStorage.deleteAll();
    } catch (e) {
      throw SecurityException('Tüm güvenli veriler silinemedi: $e');
    }
  }
  
  /// Auth token kaydet
  Future<void> saveAuthToken(String token) async {
    await saveSecureData(AppConstants.tokenKey, token);
  }
  
  /// Auth token oku
  Future<String?> getAuthToken() async {
    return await readSecureData(AppConstants.tokenKey);
  }
  
  /// Auth token sil
  Future<void> deleteAuthToken() async {
    await deleteSecureData(AppConstants.tokenKey);
  }
  
  /// Kullanıcı ID'si kaydet
  Future<void> saveUserId(String userId) async {
    await saveSecureData(AppConstants.userIdKey, userId);
  }
  
  /// Kullanıcı ID'si oku
  Future<String?> getUserId() async {
    return await readSecureData(AppConstants.userIdKey);
  }
  
  /// Kullanıcı rolü kaydet
  Future<void> saveUserRole(String role) async {
    await saveSecureData(AppConstants.userRoleKey, role);
  }
  
  /// Kullanıcı rolü oku
  Future<String?> getUserRole() async {
    return await readSecureData(AppConstants.userRoleKey);
  }
  
  /// Cihaz ID'si al
  Future<String?> getDeviceId() async {
    return await _secureStorage.read(key: _deviceIdKeyName);
  }
  
  /// Session token oluştur ve kaydet
  Future<String> createSessionToken() async {
    final token = generateSecureToken();
    await _secureStorage.write(key: _sessionTokenKeyName, value: token);
    return token;
  }
  
  /// Session token doğrula
  Future<bool> validateSessionToken(String token) async {
    final storedToken = await _secureStorage.read(key: _sessionTokenKeyName);
    return storedToken == token;
  }
  
  /// Session token sil
  Future<void> clearSessionToken() async {
    await _secureStorage.delete(key: _sessionTokenKeyName);
  }
  
  /// Güvenlik kontrolü yap
  Future<bool> performSecurityCheck() async {
    try {
      // Cihaz ID kontrolü
      final deviceId = await getDeviceId();
      if (deviceId == null) return false;
      
      // Master key kontrolü
      final masterKey = await _secureStorage.read(key: _masterKeyName);
      if (masterKey == null) return false;
      
      // Auth token kontrolü
      final authToken = await getAuthToken();
      if (authToken == null) return false;
      
      return true;
    } catch (e) {
      return false;
    }
  }
  
  /// Güvenlik verilerini temizle (çıkış işlemi için)
  Future<void> clearSecurityData() async {
    await deleteAuthToken();
    await clearSessionToken();
    // Master key ve device ID'yi koruyoruz
  }
  
  /// JWT token parse et
  Map<String, dynamic>? parseJwtToken(String token) {
    try {
      final parts = token.split('.');
      if (parts.length != 3) return null;
      
      final payload = parts[1];
      final normalized = base64.normalize(payload);
      final decoded = utf8.decode(base64.decode(normalized));
      return json.decode(decoded);
    } catch (e) {
      return null;
    }
  }
  
  /// JWT token'ın geçerliliğini kontrol et
  bool isJwtTokenValid(String token) {
    final payload = parseJwtToken(token);
    if (payload == null) return false;
    
    final exp = payload['exp'];
    if (exp == null) return false;
    
    final expirationDate = DateTime.fromMillisecondsSinceEpoch(exp * 1000);
    return DateTime.now().isBefore(expirationDate);
  }
}

/// Güvenlik exception sınıfı
class SecurityException implements Exception {
  final String message;
  SecurityException(this.message);
  
  @override
  String toString() => 'SecurityException: $message';
}
