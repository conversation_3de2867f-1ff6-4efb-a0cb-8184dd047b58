import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';

import '../../../../core/constants/app_constants.dart';
import '../../../../core/services/security_service.dart';
import '../../../../shared/themes/app_theme.dart';
import 'login_page.dart';
import '../../../tracking/presentation/pages/tracking_page.dart';

class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage>
    with TickerProviderStateMixin {
  late AnimationController _logoController;
  late AnimationController _textController;
  late AnimationController _progressController;
  
  late Animation<double> _logoAnimation;
  late Animation<double> _textAnimation;
  late Animation<double> _progressAnimation;
  
  String _statusText = 'Başlatılıyor...';
  double _progress = 0.0;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startInitialization();
  }

  void _initializeAnimations() {
    // Logo animasyonu
    _logoController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _logoAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: Curves.elasticOut,
    ));

    // Metin animasyonu
    _textController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _textAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _textController,
      curve: Curves.easeInOut,
    ));

    // Progress animasyonu
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeInOut,
    ));
  }

  Future<void> _startInitialization() async {
    // Animasyonları başlat
    _logoController.forward();
    
    await Future.delayed(const Duration(milliseconds: 500));
    _textController.forward();
    
    await Future.delayed(const Duration(milliseconds: 300));
    _progressController.forward();

    // Başlatma işlemleri
    await _performInitializationSteps();
  }

  Future<void> _performInitializationSteps() async {
    try {
      // Adım 1: Güvenlik kontrolü
      _updateStatus('Güvenlik kontrolleri yapılıyor...', 0.2);
      await Future.delayed(const Duration(milliseconds: 800));
      
      final securityCheck = await SecurityService.instance.performSecurityCheck();
      
      // Adım 2: Kullanıcı oturumu kontrolü
      _updateStatus('Oturum kontrol ediliyor...', 0.4);
      await Future.delayed(const Duration(milliseconds: 600));
      
      final authToken = await SecurityService.instance.getAuthToken();
      final isTokenValid = authToken != null && 
          SecurityService.instance.isJwtTokenValid(authToken);
      
      // Adım 3: Sistem hazırlığı
      _updateStatus('Sistem hazırlanıyor...', 0.6);
      await Future.delayed(const Duration(milliseconds: 600));
      
      // Adım 4: Bağlantı kontrolü
      _updateStatus('Bağlantı kontrol ediliyor...', 0.8);
      await Future.delayed(const Duration(milliseconds: 600));
      
      // Adım 5: Tamamlanıyor
      _updateStatus('Tamamlanıyor...', 1.0);
      await Future.delayed(const Duration(milliseconds: 500));
      
      // Navigasyon kararı
      if (securityCheck && isTokenValid) {
        _navigateToTracking();
      } else {
        _navigateToLogin();
      }
      
    } catch (e) {
      _updateStatus('Hata oluştu: ${e.toString()}', 1.0);
      await Future.delayed(const Duration(milliseconds: 2000));
      _navigateToLogin();
    }
  }

  void _updateStatus(String status, double progress) {
    if (mounted) {
      setState(() {
        _statusText = status;
        _progress = progress;
      });
    }
  }

  void _navigateToLogin() {
    if (mounted) {
      Navigator.of(context).pushReplacement(
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) => 
              const LoginPage(),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(opacity: animation, child: child);
          },
          transitionDuration: const Duration(milliseconds: 500),
        ),
      );
    }
  }

  void _navigateToTracking() {
    if (mounted) {
      Navigator.of(context).pushReplacement(
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) => 
              const TrackingPage(),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(opacity: animation, child: child);
          },
          transitionDuration: const Duration(milliseconds: 500),
        ),
      );
    }
  }

  @override
  void dispose() {
    _logoController.dispose();
    _textController.dispose();
    _progressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.primaryGradient,
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Üst boşluk
              const Spacer(flex: 2),
              
              // Logo ve başlık
              Expanded(
                flex: 3,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Logo animasyonu
                    AnimatedBuilder(
                      animation: _logoAnimation,
                      builder: (context, child) {
                        return Transform.scale(
                          scale: _logoAnimation.value,
                          child: Container(
                            width: 120,
                            height: 120,
                            decoration: BoxDecoration(
                              color: AppTheme.accentColor,
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color: AppTheme.accentColor.withOpacity(0.3),
                                  blurRadius: 20,
                                  spreadRadius: 5,
                                ),
                              ],
                            ),
                            child: const Icon(
                              Icons.military_tech,
                              size: 60,
                              color: Colors.white,
                            ),
                          ),
                        );
                      },
                    ),
                    
                    const SizedBox(height: AppConstants.largeSpacing),
                    
                    // Uygulama adı animasyonu
                    AnimatedBuilder(
                      animation: _textAnimation,
                      builder: (context, child) {
                        return Opacity(
                          opacity: _textAnimation.value,
                          child: Column(
                            children: [
                              Text(
                                AppConstants.appName.toUpperCase(),
                                style: Theme.of(context).textTheme.displayLarge?.copyWith(
                                  fontFamily: AppConstants.militaryFont,
                                  letterSpacing: 4.0,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                  shadows: [
                                    const Shadow(
                                      offset: Offset(2, 2),
                                      blurRadius: 4,
                                      color: Colors.black26,
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(height: AppConstants.smallSpacing),
                              Text(
                                AppConstants.appDescription,
                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                  color: Colors.white70,
                                  letterSpacing: 1.5,
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
              
              // Progress ve durum
              Expanded(
                flex: 2,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Progress bar
                    AnimatedBuilder(
                      animation: _progressAnimation,
                      builder: (context, child) {
                        return Container(
                          width: MediaQuery.of(context).size.width * 0.7,
                          height: 4,
                          decoration: BoxDecoration(
                            color: Colors.white24,
                            borderRadius: BorderRadius.circular(2),
                          ),
                          child: FractionallySizedBox(
                            alignment: Alignment.centerLeft,
                            widthFactor: _progress,
                            child: Container(
                              decoration: BoxDecoration(
                                color: AppTheme.accentColor,
                                borderRadius: BorderRadius.circular(2),
                                boxShadow: [
                                  BoxShadow(
                                    color: AppTheme.accentColor.withOpacity(0.5),
                                    blurRadius: 8,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                    
                    const SizedBox(height: AppConstants.mediumSpacing),
                    
                    // Durum metni
                    Text(
                      _statusText,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.white70,
                      ),
                    ),
                    
                    const SizedBox(height: AppConstants.smallSpacing),
                    
                    // Yüzde göstergesi
                    Text(
                      '${(_progress * 100).toInt()}%',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.white54,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              
              // Alt boşluk
              const Spacer(flex: 1),
              
              // Versiyon bilgisi
              Padding(
                padding: const EdgeInsets.only(bottom: AppConstants.mediumSpacing),
                child: Text(
                  'v${AppConstants.appVersion}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.white38,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
