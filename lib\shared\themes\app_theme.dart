import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../core/constants/app_constants.dart';

class AppTheme {
  // Renkler
  static const Color primaryColor = Color(0xFF1A1A1A);
  static const Color secondaryColor = Color(0xFF2D5016);
  static const Color accentColor = Color(0xFF4CAF50);
  static const Color backgroundColor = Color(0xFF0D0D0D);
  static const Color surfaceColor = Color(0xFF1E1E1E);
  static const Color cardColor = Color(0xFF2A2A2A);
  
  static const Color textPrimaryColor = Color(0xFFFFFFFF);
  static const Color textSecondaryColor = Color(0xFFB0B0B0);
  static const Color textDisabledColor = Color(0xFF666666);
  
  static const Color errorColor = Color(0xFFF44336);
  static const Color warningColor = Color(0xFFFF9800);
  static const Color successColor = Color(0xFF4CAF50);
  static const Color infoColor = Color(0xFF2196F3);
  
  // Askeri renkler
  static const Color militaryGreen = Color(0xFF2D5016);
  static const Color militaryOlive = Color(0xFF6B8E23);
  static const Color militaryKhaki = Color(0xFF8B7D3A);
  static const Color militaryCamo = Color(0xFF3C4142);
  
  // Gradient renkler
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFF1A1A1A),
      Color(0xFF2D5016),
    ],
  );
  
  static const LinearGradient accentGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFF4CAF50),
      Color(0xFF2D5016),
    ],
  );

  // Ana tema
  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      
      // Ana renkler
      primarySwatch: _createMaterialColor(primaryColor),
      primaryColor: primaryColor,
      colorScheme: const ColorScheme.dark(
        primary: accentColor,
        secondary: secondaryColor,
        surface: surfaceColor,
        background: backgroundColor,
        error: errorColor,
        onPrimary: textPrimaryColor,
        onSecondary: textPrimaryColor,
        onSurface: textPrimaryColor,
        onBackground: textPrimaryColor,
        onError: textPrimaryColor,
      ),
      
      // Scaffold
      scaffoldBackgroundColor: backgroundColor,
      
      // AppBar
      appBarTheme: const AppBarTheme(
        backgroundColor: primaryColor,
        foregroundColor: textPrimaryColor,
        elevation: 0,
        centerTitle: true,
        systemOverlayStyle: SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.light,
          systemNavigationBarColor: primaryColor,
          systemNavigationBarIconBrightness: Brightness.light,
        ),
        titleTextStyle: TextStyle(
          color: textPrimaryColor,
          fontSize: AppConstants.titleTextSize,
          fontWeight: FontWeight.bold,
          fontFamily: AppConstants.primaryFont,
        ),
      ),
      
      // Card
      cardTheme: CardTheme(
        color: cardColor,
        elevation: AppConstants.mediumElevation,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.mediumRadius),
        ),
      ),
      
      // Elevated Button
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: accentColor,
          foregroundColor: textPrimaryColor,
          elevation: AppConstants.mediumElevation,
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.largeSpacing,
            vertical: AppConstants.mediumSpacing,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.mediumRadius),
          ),
          textStyle: const TextStyle(
            fontSize: AppConstants.bodyTextSize,
            fontWeight: FontWeight.w600,
            fontFamily: AppConstants.primaryFont,
          ),
        ),
      ),
      
      // Outlined Button
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: accentColor,
          side: const BorderSide(color: accentColor, width: 2),
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.largeSpacing,
            vertical: AppConstants.mediumSpacing,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.mediumRadius),
          ),
          textStyle: const TextStyle(
            fontSize: AppConstants.bodyTextSize,
            fontWeight: FontWeight.w600,
            fontFamily: AppConstants.primaryFont,
          ),
        ),
      ),
      
      // Text Button
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: accentColor,
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.mediumSpacing,
            vertical: AppConstants.smallSpacing,
          ),
          textStyle: const TextStyle(
            fontSize: AppConstants.bodyTextSize,
            fontWeight: FontWeight.w500,
            fontFamily: AppConstants.primaryFont,
          ),
        ),
      ),
      
      // Input Decoration
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: surfaceColor,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.mediumRadius),
          borderSide: const BorderSide(color: textSecondaryColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.mediumRadius),
          borderSide: const BorderSide(color: textSecondaryColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.mediumRadius),
          borderSide: const BorderSide(color: accentColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.mediumRadius),
          borderSide: const BorderSide(color: errorColor),
        ),
        labelStyle: const TextStyle(
          color: textSecondaryColor,
          fontSize: AppConstants.bodyTextSize,
          fontFamily: AppConstants.primaryFont,
        ),
        hintStyle: const TextStyle(
          color: textDisabledColor,
          fontSize: AppConstants.bodyTextSize,
          fontFamily: AppConstants.primaryFont,
        ),
      ),
      
      // Text Theme
      textTheme: const TextTheme(
        displayLarge: TextStyle(
          color: textPrimaryColor,
          fontSize: 32,
          fontWeight: FontWeight.bold,
          fontFamily: AppConstants.primaryFont,
        ),
        displayMedium: TextStyle(
          color: textPrimaryColor,
          fontSize: 28,
          fontWeight: FontWeight.bold,
          fontFamily: AppConstants.primaryFont,
        ),
        displaySmall: TextStyle(
          color: textPrimaryColor,
          fontSize: AppConstants.titleTextSize,
          fontWeight: FontWeight.bold,
          fontFamily: AppConstants.primaryFont,
        ),
        headlineLarge: TextStyle(
          color: textPrimaryColor,
          fontSize: 22,
          fontWeight: FontWeight.w600,
          fontFamily: AppConstants.primaryFont,
        ),
        headlineMedium: TextStyle(
          color: textPrimaryColor,
          fontSize: 20,
          fontWeight: FontWeight.w600,
          fontFamily: AppConstants.primaryFont,
        ),
        headlineSmall: TextStyle(
          color: textPrimaryColor,
          fontSize: AppConstants.subtitleTextSize,
          fontWeight: FontWeight.w600,
          fontFamily: AppConstants.primaryFont,
        ),
        titleLarge: TextStyle(
          color: textPrimaryColor,
          fontSize: AppConstants.bodyTextSize,
          fontWeight: FontWeight.w500,
          fontFamily: AppConstants.primaryFont,
        ),
        titleMedium: TextStyle(
          color: textPrimaryColor,
          fontSize: AppConstants.captionTextSize,
          fontWeight: FontWeight.w500,
          fontFamily: AppConstants.primaryFont,
        ),
        titleSmall: TextStyle(
          color: textSecondaryColor,
          fontSize: AppConstants.smallTextSize,
          fontWeight: FontWeight.w500,
          fontFamily: AppConstants.primaryFont,
        ),
        bodyLarge: TextStyle(
          color: textPrimaryColor,
          fontSize: AppConstants.bodyTextSize,
          fontWeight: FontWeight.normal,
          fontFamily: AppConstants.primaryFont,
        ),
        bodyMedium: TextStyle(
          color: textPrimaryColor,
          fontSize: AppConstants.captionTextSize,
          fontWeight: FontWeight.normal,
          fontFamily: AppConstants.primaryFont,
        ),
        bodySmall: TextStyle(
          color: textSecondaryColor,
          fontSize: AppConstants.smallTextSize,
          fontWeight: FontWeight.normal,
          fontFamily: AppConstants.primaryFont,
        ),
        labelLarge: TextStyle(
          color: textPrimaryColor,
          fontSize: AppConstants.captionTextSize,
          fontWeight: FontWeight.w500,
          fontFamily: AppConstants.primaryFont,
        ),
        labelMedium: TextStyle(
          color: textSecondaryColor,
          fontSize: AppConstants.smallTextSize,
          fontWeight: FontWeight.w500,
          fontFamily: AppConstants.primaryFont,
        ),
        labelSmall: TextStyle(
          color: textDisabledColor,
          fontSize: 10,
          fontWeight: FontWeight.w500,
          fontFamily: AppConstants.primaryFont,
        ),
      ),
      
      // Icon Theme
      iconTheme: const IconThemeData(
        color: textPrimaryColor,
        size: AppConstants.mediumIconSize,
      ),
      
      // Bottom Navigation Bar
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: primaryColor,
        selectedItemColor: accentColor,
        unselectedItemColor: textSecondaryColor,
        type: BottomNavigationBarType.fixed,
        elevation: AppConstants.highElevation,
      ),
      
      // Floating Action Button
      floatingActionButtonTheme: const FloatingActionButtonThemeData(
        backgroundColor: accentColor,
        foregroundColor: textPrimaryColor,
        elevation: AppConstants.mediumElevation,
      ),
      
      // Divider
      dividerTheme: const DividerThemeData(
        color: textDisabledColor,
        thickness: 1,
      ),
      
      // Switch
      switchTheme: SwitchThemeData(
        thumbColor: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.selected)) {
            return accentColor;
          }
          return textSecondaryColor;
        }),
        trackColor: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.selected)) {
            return accentColor.withOpacity(0.5);
          }
          return textDisabledColor;
        }),
      ),
    );
  }
  
  // Material Color oluşturucu
  static MaterialColor _createMaterialColor(Color color) {
    List strengths = <double>[.05];
    Map<int, Color> swatch = {};
    final int r = color.red, g = color.green, b = color.blue;

    for (int i = 1; i < 10; i++) {
      strengths.add(0.1 * i);
    }
    for (var strength in strengths) {
      final double ds = 0.5 - strength;
      swatch[(strength * 1000).round()] = Color.fromRGBO(
        r + ((ds < 0 ? r : (255 - r)) * ds).round(),
        g + ((ds < 0 ? g : (255 - g)) * ds).round(),
        b + ((ds < 0 ? b : (255 - b)) * ds).round(),
        1,
      );
    }
    return MaterialColor(color.value, swatch);
  }
}
