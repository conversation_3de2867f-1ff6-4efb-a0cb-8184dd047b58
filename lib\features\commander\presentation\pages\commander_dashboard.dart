import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';
import 'dart:math';

import '../../../../core/constants/app_constants.dart';
import '../../../../shared/themes/app_theme.dart';
import '../../../../shared/widgets/custom_button.dart';

class CommanderDashboard extends StatefulWidget {
  const CommanderDashboard({super.key});

  @override
  State<CommanderDashboard> createState() => _CommanderDashboardState();
}

class _CommanderDashboardState extends State<CommanderDashboard>
    with TickerProviderStateMixin {
  late AnimationController _refreshController;
  late Animation<double> _refreshAnimation;
  
  int _selectedIndex = 0;
  List<SoldierData> _soldiers = [];
  Timer? _updateTimer;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _generateMockData();
    _startAutoRefresh();
  }

  void _initializeAnimations() {
    _refreshController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _refreshAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _refreshController,
      curve: Curves.easeInOut,
    ));
  }

  void _generateMockData() {
    final random = Random();
    _soldiers = List.generate(8, (index) {
      return SoldierData(
        id: 'ASK${(index + 1).toString().padLeft(3, '0')}',
        name: 'Asker ${index + 1}',
        latitude: 41.0082 + (random.nextDouble() - 0.5) * 0.01,
        longitude: 28.9784 + (random.nextDouble() - 0.5) * 0.01,
        status: SoldierStatus.values[random.nextInt(SoldierStatus.values.length)],
        lastUpdate: DateTime.now().subtract(Duration(minutes: random.nextInt(30))),
        batteryLevel: 20 + random.nextInt(80),
        signalStrength: 1 + random.nextInt(4),
      );
    });
  }

  void _startAutoRefresh() {
    _updateTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      _refreshData();
    });
  }

  void _refreshData() {
    _refreshController.forward().then((_) {
      _refreshController.reset();
    });
    
    // Mock data güncelleme
    final random = Random();
    for (var soldier in _soldiers) {
      soldier.latitude += (random.nextDouble() - 0.5) * 0.0001;
      soldier.longitude += (random.nextDouble() - 0.5) * 0.0001;
      soldier.lastUpdate = DateTime.now().subtract(Duration(seconds: random.nextInt(300)));
      soldier.batteryLevel = max(0, soldier.batteryLevel + random.nextInt(3) - 1);
    }
    
    if (mounted) {
      setState(() {});
    }
  }

  @override
  void dispose() {
    _refreshController.dispose();
    _updateTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'KOMUTAN PANELİ',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            letterSpacing: 1.5,
          ),
        ),
        actions: [
          AnimatedBuilder(
            animation: _refreshAnimation,
            builder: (context, child) {
              return Transform.rotate(
                angle: _refreshAnimation.value * 2 * pi,
                child: IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: _refreshData,
                  tooltip: 'Yenile',
                ),
              );
            },
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.primaryGradient,
        ),
        child: Column(
          children: [
            // İstatistik kartları
            _buildStatsCards(),
            
            // Tab bar
            _buildTabBar(),
            
            // İçerik
            Expanded(
              child: _buildTabContent(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsCards() {
    final activeSoldiers = _soldiers.where((s) => s.status == SoldierStatus.active).length;
    final offlineSoldiers = _soldiers.where((s) => s.status == SoldierStatus.offline).length;
    final emergencySoldiers = _soldiers.where((s) => s.status == SoldierStatus.emergency).length;

    return Container(
      padding: const EdgeInsets.all(AppConstants.mediumSpacing),
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              'AKTİF',
              activeSoldiers.toString(),
              AppTheme.successColor,
              Icons.check_circle,
            ),
          ),
          const SizedBox(width: AppConstants.smallSpacing),
          Expanded(
            child: _buildStatCard(
              'ÇEVRİMDIŞI',
              offlineSoldiers.toString(),
              AppTheme.warningColor,
              Icons.offline_bolt,
            ),
          ),
          const SizedBox(width: AppConstants.smallSpacing),
          Expanded(
            child: _buildStatCard(
              'ACİL DURUM',
              emergencySoldiers.toString(),
              AppTheme.errorColor,
              Icons.emergency,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color, IconData icon) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.mediumSpacing),
        child: Column(
          children: [
            Icon(
              icon,
              color: color,
              size: 24,
            ),
            const SizedBox(height: AppConstants.smallSpacing),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppTheme.textSecondaryColor,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppConstants.mediumSpacing),
      decoration: BoxDecoration(
        color: AppTheme.cardColor,
        borderRadius: BorderRadius.circular(AppConstants.mediumRadius),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildTabButton('LİSTE', 0, Icons.list),
          ),
          Expanded(
            child: _buildTabButton('HARİTA', 1, Icons.map),
          ),
          Expanded(
            child: _buildTabButton('MESAJLAR', 2, Icons.message),
          ),
        ],
      ),
    );
  }

  Widget _buildTabButton(String title, int index, IconData icon) {
    final isSelected = _selectedIndex == index;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedIndex = index;
        });
        HapticFeedback.lightImpact();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: AppConstants.mediumSpacing),
        decoration: BoxDecoration(
          color: isSelected ? AppTheme.accentColor : Colors.transparent,
          borderRadius: BorderRadius.circular(AppConstants.mediumRadius),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: isSelected ? Colors.white : AppTheme.textSecondaryColor,
              size: 20,
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: TextStyle(
                color: isSelected ? Colors.white : AppTheme.textSecondaryColor,
                fontSize: AppConstants.smallTextSize,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTabContent() {
    switch (_selectedIndex) {
      case 0:
        return _buildSoldiersList();
      case 1:
        return _buildMapView();
      case 2:
        return _buildMessagesView();
      default:
        return _buildSoldiersList();
    }
  }

  Widget _buildSoldiersList() {
    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.mediumSpacing),
      itemCount: _soldiers.length,
      itemBuilder: (context, index) {
        final soldier = _soldiers[index];
        return _buildSoldierCard(soldier);
      },
    );
  }

  Widget _buildSoldierCard(SoldierData soldier) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.smallSpacing),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.mediumSpacing),
        child: Column(
          children: [
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: _getStatusColor(soldier.status),
                  child: Text(
                    soldier.id.substring(3),
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: AppConstants.smallTextSize,
                    ),
                  ),
                ),
                const SizedBox(width: AppConstants.mediumSpacing),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        soldier.name,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        soldier.id,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppConstants.smallSpacing,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: _getStatusColor(soldier.status),
                    borderRadius: BorderRadius.circular(AppConstants.smallRadius),
                  ),
                  child: Text(
                    _getStatusText(soldier.status),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: AppConstants.smallTextSize,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.smallSpacing),
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    Icons.location_on,
                    '${soldier.latitude.toStringAsFixed(4)}, ${soldier.longitude.toStringAsFixed(4)}',
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.smallSpacing),
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    Icons.access_time,
                    _formatLastUpdate(soldier.lastUpdate),
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    Icons.battery_std,
                    '%${soldier.batteryLevel}',
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    Icons.signal_cellular_alt,
                    '${'●' * soldier.signalStrength}${'○' * (4 - soldier.signalStrength)}',
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(IconData icon, String text) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: AppTheme.textSecondaryColor,
        ),
        const SizedBox(width: 4),
        Expanded(
          child: Text(
            text,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildMapView() {
    return Container(
      margin: const EdgeInsets.all(AppConstants.mediumSpacing),
      decoration: BoxDecoration(
        color: AppTheme.cardColor,
        borderRadius: BorderRadius.circular(AppConstants.mediumRadius),
      ),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.map,
              size: 64,
              color: AppTheme.textSecondaryColor,
            ),
            SizedBox(height: AppConstants.mediumSpacing),
            Text(
              'HARİTA GÖRÜNÜMÜ',
              style: TextStyle(
                color: AppTheme.textSecondaryColor,
                fontSize: AppConstants.titleTextSize,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: AppConstants.smallSpacing),
            Text(
              'Google Maps entegrasyonu\ngeliştirilme aşamasında',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: AppTheme.textSecondaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMessagesView() {
    return Container(
      margin: const EdgeInsets.all(AppConstants.mediumSpacing),
      decoration: BoxDecoration(
        color: AppTheme.cardColor,
        borderRadius: BorderRadius.circular(AppConstants.mediumRadius),
      ),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.message,
              size: 64,
              color: AppTheme.textSecondaryColor,
            ),
            SizedBox(height: AppConstants.mediumSpacing),
            Text(
              'MESAJLAŞMA',
              style: TextStyle(
                color: AppTheme.textSecondaryColor,
                fontSize: AppConstants.titleTextSize,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: AppConstants.smallSpacing),
            Text(
              'Güvenli mesajlaşma sistemi\ngeliştirilme aşamasında',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: AppTheme.textSecondaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(SoldierStatus status) {
    switch (status) {
      case SoldierStatus.active:
        return AppTheme.successColor;
      case SoldierStatus.inactive:
        return AppTheme.warningColor;
      case SoldierStatus.offline:
        return AppTheme.textDisabledColor;
      case SoldierStatus.emergency:
        return AppTheme.errorColor;
    }
  }

  String _getStatusText(SoldierStatus status) {
    switch (status) {
      case SoldierStatus.active:
        return 'AKTİF';
      case SoldierStatus.inactive:
        return 'PASİF';
      case SoldierStatus.offline:
        return 'ÇEVRİMDIŞI';
      case SoldierStatus.emergency:
        return 'ACİL DURUM';
    }
  }

  String _formatLastUpdate(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inMinutes < 1) {
      return 'Şimdi';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}dk önce';
    } else {
      return '${difference.inHours}sa önce';
    }
  }
}

// Veri modelleri
class SoldierData {
  final String id;
  final String name;
  double latitude;
  double longitude;
  final SoldierStatus status;
  DateTime lastUpdate;
  int batteryLevel;
  final int signalStrength;

  SoldierData({
    required this.id,
    required this.name,
    required this.latitude,
    required this.longitude,
    required this.status,
    required this.lastUpdate,
    required this.batteryLevel,
    required this.signalStrength,
  });
}

enum SoldierStatus {
  active,
  inactive,
  offline,
  emergency,
}
