import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:partizan/main.dart';
import 'package:partizan/core/services/security_service.dart';
import 'package:partizan/core/services/location_service.dart';

void main() {
  group('Partizan App Tests', () {
    setUpAll(() async {
      // Test ortamı için servisleri başlat
      await SecurityService.initialize();
      await LocationService.initialize();
    });

    testWidgets('App should start with splash screen', (WidgetTester tester) async {
      // Uygulamayı başlat
      await tester.pumpWidget(
        const ProviderScope(
          child: PartizanApp(),
        ),
      );

      // Splash screen'in yüklenmesini bekle
      await tester.pump();

      // Partizan logosu ve başlığının görünür olduğunu kontrol et
      expect(find.text('PARTIZAN'), findsOneWidget);
      expect(find.text('Asker Ta<PERSON>p Uygulaması'), findsOneWidget);
      expect(find.byIcon(Icons.military_tech), findsOneWidget);
    });

    testWidgets('Should show login page after splash', (WidgetTester tester) async {
      await tester.pumpWidget(
        const ProviderScope(
          child: PartizanApp(),
        ),
      );

      // Splash animasyonlarının tamamlanmasını bekle
      await tester.pumpAndSettle(const Duration(seconds: 5));

      // Login sayfasının yüklenmesini kontrol et
      expect(find.text('GİRİŞ'), findsOneWidget);
      expect(find.text('Kullanıcı Adı'), findsOneWidget);
      expect(find.text('Şifre'), findsOneWidget);
      expect(find.text('GİRİŞ YAP'), findsOneWidget);
    });

    testWidgets('Login form validation should work', (WidgetTester tester) async {
      await tester.pumpWidget(
        const ProviderScope(
          child: PartizanApp(),
        ),
      );

      // Login sayfasına git
      await tester.pumpAndSettle(const Duration(seconds: 5));

      // Boş form ile giriş yapmaya çalış
      final loginButton = find.text('GİRİŞ YAP');
      await tester.tap(loginButton);
      await tester.pump();

      // Validation mesajlarının görünür olduğunu kontrol et
      expect(find.text('Kullanıcı adı gerekli'), findsOneWidget);
      expect(find.text('Şifre gerekli'), findsOneWidget);
    });

    testWidgets('Successful login should navigate to tracking page', (WidgetTester tester) async {
      await tester.pumpWidget(
        const ProviderScope(
          child: PartizanApp(),
        ),
      );

      // Login sayfasına git
      await tester.pumpAndSettle(const Duration(seconds: 5));

      // Kullanıcı adı ve şifre gir
      await tester.enterText(find.byType(TextFormField).first, 'komutan');
      await tester.enterText(find.byType(TextFormField).last, 'partizan2024');

      // Giriş yap
      await tester.tap(find.text('GİRİŞ YAP'));
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Tracking sayfasının yüklenmesini kontrol et
      expect(find.text('KOMUTAN PANELİ'), findsOneWidget);
      expect(find.text('DURUM'), findsOneWidget);
      expect(find.text('KONUM BİLGİLERİ'), findsOneWidget);
    });

    testWidgets('Commander should access dashboard', (WidgetTester tester) async {
      await tester.pumpWidget(
        const ProviderScope(
          child: PartizanApp(),
        ),
      );

      // Login işlemi
      await tester.pumpAndSettle(const Duration(seconds: 5));
      await tester.enterText(find.byType(TextFormField).first, 'komutan');
      await tester.enterText(find.byType(TextFormField).last, 'partizan2024');
      await tester.tap(find.text('GİRİŞ YAP'));
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Dashboard butonuna tıkla
      await tester.tap(find.byIcon(Icons.dashboard));
      await tester.pumpAndSettle();

      // Dashboard'un yüklenmesini kontrol et
      expect(find.text('KOMUTAN PANELİ'), findsOneWidget);
      expect(find.text('AKTİF'), findsOneWidget);
      expect(find.text('ÇEVRİMDIŞI'), findsOneWidget);
      expect(find.text('ACİL DURUM'), findsOneWidget);
    });

    testWidgets('Soldier login should work', (WidgetTester tester) async {
      await tester.pumpWidget(
        const ProviderScope(
          child: PartizanApp(),
        ),
      );

      // Login işlemi
      await tester.pumpAndSettle(const Duration(seconds: 5));
      await tester.enterText(find.byType(TextFormField).first, 'asker');
      await tester.enterText(find.byType(TextFormField).last, 'partizan2024');
      await tester.tap(find.text('GİRİŞ YAP'));
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Asker panelinin yüklenmesini kontrol et
      expect(find.text('ASKER TAKİP'), findsOneWidget);
      expect(find.text('ASKER'), findsOneWidget);
    });

    testWidgets('Emergency button should work', (WidgetTester tester) async {
      await tester.pumpWidget(
        const ProviderScope(
          child: PartizanApp(),
        ),
      );

      // Login işlemi
      await tester.pumpAndSettle(const Duration(seconds: 5));
      await tester.enterText(find.byType(TextFormField).first, 'asker');
      await tester.enterText(find.byType(TextFormField).last, 'partizan2024');
      await tester.tap(find.text('GİRİŞ YAP'));
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Acil durum butonuna tıkla
      await tester.tap(find.text('ACİL DURUM'));
      await tester.pump();

      // Snackbar'ın görünür olduğunu kontrol et
      expect(find.text('Acil durum sinyali gönderildi!'), findsOneWidget);
    });

    testWidgets('Logout should work', (WidgetTester tester) async {
      await tester.pumpWidget(
        const ProviderScope(
          child: PartizanApp(),
        ),
      );

      // Login işlemi
      await tester.pumpAndSettle(const Duration(seconds: 5));
      await tester.enterText(find.byType(TextFormField).first, 'komutan');
      await tester.enterText(find.byType(TextFormField).last, 'partizan2024');
      await tester.tap(find.text('GİRİŞ YAP'));
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Çıkış butonuna tıkla
      await tester.tap(find.byIcon(Icons.logout));
      await tester.pumpAndSettle(const Duration(seconds: 2));

      // Login sayfasına dönüldüğünü kontrol et
      expect(find.text('GİRİŞ'), findsOneWidget);
    });
  });

  group('Security Service Tests', () {
    test('Should generate secure token', () {
      final token = SecurityService.instance.generateSecureToken();
      expect(token, isNotEmpty);
      expect(token.length, equals(64)); // SHA-256 hash length
    });

    test('Should hash password correctly', () {
      const password = 'testpassword';
      const salt = 'testsalt';
      
      final hash1 = SecurityService.instance.hashPassword(password, salt);
      final hash2 = SecurityService.instance.hashPassword(password, salt);
      
      expect(hash1, equals(hash2)); // Same input should produce same hash
      expect(hash1, isNotEmpty);
    });

    test('Should generate salt', () {
      final salt1 = SecurityService.instance.generateSalt();
      final salt2 = SecurityService.instance.generateSalt();
      
      expect(salt1, isNotEmpty);
      expect(salt2, isNotEmpty);
      expect(salt1, isNot(equals(salt2))); // Different salts each time
    });

    test('Should encrypt and decrypt data', () {
      const testData = 'Bu bir test verisidir';
      
      final encrypted = SecurityService.instance.encryptData(testData);
      final decrypted = SecurityService.instance.decryptData(encrypted);
      
      expect(encrypted, isNot(equals(testData))); // Should be different
      expect(decrypted, equals(testData)); // Should be same after decryption
    });
  });

  group('Location Service Tests', () {
    test('Should validate location data', () {
      final validData = {
        'latitude': 41.0082,
        'longitude': 28.9784,
      };
      
      final invalidData = {
        'latitude': 91.0, // Invalid latitude
        'longitude': 181.0, // Invalid longitude
      };
      
      expect(LocationService.instance.validateLocationData(validData), isTrue);
      expect(LocationService.instance.validateLocationData(invalidData), isFalse);
    });

    test('Should calculate distance correctly', () {
      // İstanbul - Ankara arası yaklaşık mesafe
      const istanbulLat = 41.0082;
      const istanbulLng = 28.9784;
      const ankaraLat = 39.9334;
      const ankaraLng = 32.8597;
      
      final distance = LocationService.instance.calculateDistance(
        istanbulLat, istanbulLng, ankaraLat, ankaraLng,
      );
      
      // Yaklaşık 350-400 km olmalı
      expect(distance, greaterThan(300000)); // 300 km
      expect(distance, lessThan(500000)); // 500 km
    });
  });

  group('Custom Widget Tests', () {
    testWidgets('CustomButton should render correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CustomButton(
              text: 'Test Button',
              onPressed: () {},
            ),
          ),
        ),
      );

      expect(find.text('Test Button'), findsOneWidget);
      expect(find.byType(CustomButton), findsOneWidget);
    });

    testWidgets('CustomTextField should render correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CustomTextField(
              labelText: 'Test Field',
              controller: TextEditingController(),
            ),
          ),
        ),
      );

      expect(find.text('Test Field'), findsOneWidget);
      expect(find.byType(CustomTextField), findsOneWidget);
    });
  });
}

// Test için gerekli import'lar
import 'package:partizan/shared/widgets/custom_button.dart';
import 'package:partizan/shared/widgets/custom_text_field.dart';
