# Partizan

Partizan is a mobile application designed for real-time tracking of soldiers' locations during operations. The app provides commanders with a secure and efficient way to monitor troop movements using advanced mapping technologies.

## Features

- **Live Tracking**: Monitor the real-time positions of soldiers on the battlefield.
- **Advanced Security**: Utilizes encryption and token-based authentication to ensure data integrity and privacy.
- **User-Friendly Interface**: Modern dark-themed design with intuitive navigation.
- **Offline Capabilities**: Supports offline maps using Mapbox when internet connectivity is unavailable.

## Technologies Used

- **Flutter**: Cross-platform framework for building natively compiled applications for mobile.
- **Google Maps**: For online mapping and location services.
- **Mapbox**: For offline map capabilities.
- **Dart**: Programming language used for Flutter development.

## Installation

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/partizan.git
   ```
2. Navigate to the project directory:
   ```
   cd partizan
   ```
3. Install dependencies:
   ```
   flutter pub get
   ```
4. Run the application:
   ```
   flutter run
   ```

## Usage

- The application is designed for commanders to access the command panel and view soldier locations.
- Soldiers can be tracked in real-time, and their statuses can be updated as needed.

## Contributing

Contributions are welcome! Please open an issue or submit a pull request for any enhancements or bug fixes.

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.