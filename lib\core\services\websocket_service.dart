import 'dart:async';
import 'dart:convert';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:web_socket_channel/status.dart' as status;

import '../constants/app_constants.dart';
import 'security_service.dart';

class WebSocketService {
  static WebSocketService? _instance;
  static WebSocketService get instance => _instance ??= WebSocketService._();
  WebSocketService._();

  WebSocketChannel? _channel;
  StreamSubscription? _subscription;
  Timer? _heartbeatTimer;
  Timer? _reconnectTimer;
  
  bool _isConnected = false;
  bool _isConnecting = false;
  int _reconnectAttempts = 0;
  static const int _maxReconnectAttempts = 5;
  static const Duration _reconnectDelay = Duration(seconds: 5);
  static const Duration _heartbeatInterval = Duration(seconds: 30);

  // Stream controllers
  final StreamController<Map<String, dynamic>> _messageController = 
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<bool> _connectionController = 
      StreamController<bool>.broadcast();

  // Public streams
  Stream<Map<String, dynamic>> get messageStream => _messageController.stream;
  Stream<bool> get connectionStream => _connectionController.stream;
  bool get isConnected => _isConnected;

  /// WebSocket bağlantısını başlat
  Future<void> connect() async {
    if (_isConnected || _isConnecting) return;

    try {
      _isConnecting = true;
      
      // Auth token al
      final token = await SecurityService.instance.getAuthToken();
      if (token == null) {
        throw WebSocketException('Auth token bulunamadı');
      }

      // Device ID al
      final deviceId = await SecurityService.instance.getDeviceId();
      
      // WebSocket URL'ini oluştur
      final uri = Uri.parse('${AppConstants.wsUrl}?token=$token&device_id=$deviceId');
      
      // Bağlantı kur
      _channel = WebSocketChannel.connect(uri);
      
      // Mesajları dinle
      _subscription = _channel!.stream.listen(
        _onMessage,
        onError: _onError,
        onDone: _onDisconnected,
      );

      _isConnected = true;
      _isConnecting = false;
      _reconnectAttempts = 0;
      
      _connectionController.add(true);
      _startHeartbeat();
      
      print('✅ WebSocket bağlantısı kuruldu');
      
    } catch (e) {
      _isConnecting = false;
      _onError(e);
    }
  }

  /// WebSocket bağlantısını kapat
  Future<void> disconnect() async {
    _stopHeartbeat();
    _stopReconnectTimer();
    
    await _subscription?.cancel();
    await _channel?.sink.close(status.normalClosure);
    
    _isConnected = false;
    _connectionController.add(false);
    
    print('🔌 WebSocket bağlantısı kapatıldı');
  }

  /// Mesaj gönder
  void sendMessage(Map<String, dynamic> message) {
    if (!_isConnected || _channel == null) {
      throw WebSocketException('WebSocket bağlantısı yok');
    }

    try {
      final jsonMessage = json.encode(message);
      _channel!.sink.add(jsonMessage);
      print('📤 Mesaj gönderildi: $jsonMessage');
    } catch (e) {
      throw WebSocketException('Mesaj gönderilemedi: $e');
    }
  }

  /// Konum güncellemesi gönder
  void sendLocationUpdate(double latitude, double longitude, double accuracy) {
    sendMessage({
      'type': 'location_update',
      'data': {
        'latitude': latitude,
        'longitude': longitude,
        'accuracy': accuracy,
        'timestamp': DateTime.now().toIso8601String(),
      },
    });
  }

  /// Durum güncellemesi gönder
  void sendStatusUpdate(String status) {
    sendMessage({
      'type': 'status_update',
      'data': {
        'status': status,
        'timestamp': DateTime.now().toIso8601String(),
      },
    });
  }

  /// Mesaj gönder
  void sendChatMessage(String recipientId, String content) {
    sendMessage({
      'type': 'chat_message',
      'data': {
        'recipient_id': recipientId,
        'content': content,
        'timestamp': DateTime.now().toIso8601String(),
      },
    });
  }

  /// Acil durum sinyali gönder
  void sendEmergencySignal(double latitude, double longitude, String message) {
    sendMessage({
      'type': 'emergency',
      'data': {
        'latitude': latitude,
        'longitude': longitude,
        'message': message,
        'timestamp': DateTime.now().toIso8601String(),
      },
    });
  }

  /// Heartbeat gönder
  void _sendHeartbeat() {
    if (_isConnected) {
      sendMessage({
        'type': 'heartbeat',
        'timestamp': DateTime.now().toIso8601String(),
      });
    }
  }

  /// Mesaj alındığında çağrılır
  void _onMessage(dynamic message) {
    try {
      final Map<String, dynamic> data = json.decode(message);
      print('📥 Mesaj alındı: $data');
      
      // Heartbeat yanıtı kontrolü
      if (data['type'] == 'heartbeat_response') {
        return; // Heartbeat yanıtlarını işleme
      }
      
      _messageController.add(data);
    } catch (e) {
      print('❌ Mesaj parse hatası: $e');
    }
  }

  /// Hata oluştuğunda çağrılır
  void _onError(dynamic error) {
    print('❌ WebSocket hatası: $error');
    _isConnected = false;
    _connectionController.add(false);
    
    // Yeniden bağlanmayı dene
    _scheduleReconnect();
  }

  /// Bağlantı kesildiğinde çağrılır
  void _onDisconnected() {
    print('🔌 WebSocket bağlantısı kesildi');
    _isConnected = false;
    _connectionController.add(false);
    
    // Yeniden bağlanmayı dene
    _scheduleReconnect();
  }

  /// Heartbeat başlat
  void _startHeartbeat() {
    _heartbeatTimer = Timer.periodic(_heartbeatInterval, (timer) {
      _sendHeartbeat();
    });
  }

  /// Heartbeat durdur
  void _stopHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = null;
  }

  /// Yeniden bağlanma zamanla
  void _scheduleReconnect() {
    if (_reconnectAttempts >= _maxReconnectAttempts) {
      print('❌ Maksimum yeniden bağlanma denemesi aşıldı');
      return;
    }

    _stopReconnectTimer();
    
    _reconnectAttempts++;
    print('🔄 Yeniden bağlanma denemesi $_reconnectAttempts/$_maxReconnectAttempts');
    
    _reconnectTimer = Timer(_reconnectDelay, () {
      connect();
    });
  }

  /// Yeniden bağlanma timer'ını durdur
  void _stopReconnectTimer() {
    _reconnectTimer?.cancel();
    _reconnectTimer = null;
  }

  /// Servisi temizle
  Future<void> dispose() async {
    await disconnect();
    await _messageController.close();
    await _connectionController.close();
  }

  /// Bağlantı durumunu kontrol et
  void checkConnection() {
    if (!_isConnected && !_isConnecting) {
      connect();
    }
  }

  /// Manuel yeniden bağlanma
  Future<void> reconnect() async {
    await disconnect();
    _reconnectAttempts = 0;
    await connect();
  }

  /// Mesaj dinleyici ekle
  StreamSubscription<Map<String, dynamic>> listen(
    void Function(Map<String, dynamic>) onMessage, {
    Function? onError,
    void Function()? onDone,
  }) {
    return messageStream.listen(
      onMessage,
      onError: onError,
      onDone: onDone,
    );
  }

  /// Bağlantı durumu dinleyici ekle
  StreamSubscription<bool> listenConnection(
    void Function(bool) onConnectionChange, {
    Function? onError,
    void Function()? onDone,
  }) {
    return connectionStream.listen(
      onConnectionChange,
      onError: onError,
      onDone: onDone,
    );
  }

  /// Belirli mesaj tipini dinle
  StreamSubscription<Map<String, dynamic>> listenMessageType(
    String messageType,
    void Function(Map<String, dynamic>) onMessage, {
    Function? onError,
    void Function()? onDone,
  }) {
    return messageStream
        .where((message) => message['type'] == messageType)
        .listen(
          onMessage,
          onError: onError,
          onDone: onDone,
        );
  }

  /// Konum güncellemelerini dinle
  StreamSubscription<Map<String, dynamic>> listenLocationUpdates(
    void Function(Map<String, dynamic>) onLocationUpdate, {
    Function? onError,
    void Function()? onDone,
  }) {
    return listenMessageType(
      'location_update',
      onLocationUpdate,
      onError: onError,
      onDone: onDone,
    );
  }

  /// Chat mesajlarını dinle
  StreamSubscription<Map<String, dynamic>> listenChatMessages(
    void Function(Map<String, dynamic>) onChatMessage, {
    Function? onError,
    void Function()? onDone,
  }) {
    return listenMessageType(
      'chat_message',
      onChatMessage,
      onError: onError,
      onDone: onDone,
    );
  }

  /// Acil durum sinyallerini dinle
  StreamSubscription<Map<String, dynamic>> listenEmergencySignals(
    void Function(Map<String, dynamic>) onEmergencySignal, {
    Function? onError,
    void Function()? onDone,
  }) {
    return listenMessageType(
      'emergency',
      onEmergencySignal,
      onError: onError,
      onDone: onDone,
    );
  }

  /// Durum güncellemelerini dinle
  StreamSubscription<Map<String, dynamic>> listenStatusUpdates(
    void Function(Map<String, dynamic>) onStatusUpdate, {
    Function? onError,
    void Function()? onDone,
  }) {
    return listenMessageType(
      'status_update',
      onStatusUpdate,
      onError: onError,
      onDone: onDone,
    );
  }
}

/// WebSocket exception sınıfı
class WebSocketException implements Exception {
  final String message;
  WebSocketException(this.message);
  
  @override
  String toString() => 'WebSocketException: $message';
}
