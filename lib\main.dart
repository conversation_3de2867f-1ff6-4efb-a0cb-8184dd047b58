import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

import 'core/constants/app_constants.dart';
import 'core/services/location_service.dart';
import 'core/services/security_service.dart';
import 'shared/themes/app_theme.dart';
import 'features/auth/presentation/pages/splash_page.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Sistem çubuğunu gizle (tam ekran mod)
  SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
  
  // Sadece dikey yönlendirmeye izin ver
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);
  
  // Güvenlik servisini başlat
  await SecurityService.initialize();
  
  // Konum servisini başlat
  await LocationService.initialize();
  
  runApp(
    const ProviderScope(
      child: PartizanApp(),
    ),
  );
}

class PartizanApp extends StatelessWidget {
  const PartizanApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: AppConstants.appName,
      debugShowCheckedModeBanner: false,
      
      // Tema ayarları
      theme: AppTheme.darkTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.dark,
      
      // Dil ayarları (Türkçe)
      locale: const Locale('tr', 'TR'),
      supportedLocales: const [
        Locale('tr', 'TR'),
        Locale('en', 'US'),
      ],
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      
      // Ana sayfa
      home: const SplashPage(),
      
      // Navigasyon ayarları
      builder: (context, child) {
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(
            textScaleFactor: 1.0, // Metin boyutunu sabitle
          ),
          child: child!,
        );
      },
    );
  }
}
