# 🌐 آنلاین Build کردن اپلیکیشن پارتیزان

## 🚀 روش 1: GitHub Actions (رایگان)

### مرحله 1: آپلود به GitHub
```bash
# 1. GitHub.com بروید
# 2. New Repository بسازید: partizan-app
# 3. فایل‌های پروژه را آپلود کنید
```

### مرحله 2: GitHub Actions Setup
```yaml
# .github/workflows/build.yml
name: Build APK
on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - uses: actions/setup-java@v3
      with:
        distribution: 'zulu'
        java-version: '11'
    - uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.16.0'
    - run: flutter pub get
    - run: flutter build apk --release
    - uses: actions/upload-artifact@v3
      with:
        name: apk
        path: build/app/outputs/flutter-apk/app-release.apk
```

### مرحله 3: د<PERSON><PERSON>و<PERSON> APK
```
1. GitHub > Actions tab
2. آ<PERSON><PERSON>ین build را انتخاب کنید
3. Artifacts > apk > Download
4. فایل ZIP را extract کنید
5. APK آماده است!
```

## 🔥 روش 2: Codemagic (سریع‌ترین)

### مرحله 1: ثبت‌نام
```
1. codemagic.io بروید
2. Sign up with GitHub
3. Repository را connect کنید
```

### مرحله 2: Build Configuration
```yaml
# codemagic.yaml
workflows:
  android-workflow:
    name: Android Workflow
    max_build_duration: 120
    environment:
      flutter: stable
    scripts:
      - name: Get Flutter packages
        script: flutter packages pub get
      - name: Build APK
        script: flutter build apk --release
    artifacts:
      - build/**/outputs/**/*.apk
```

### مرحله 3: شروع Build
```
1. Start new build
2. منتظر بمانید (15-20 دقیقه)
3. APK را دانلود کنید
```

## ⚡ روش 3: Replit (آنلاین IDE)

### مرحله 1: Setup
```
1. replit.com بروید
2. Create Repl > Flutter
3. فایل‌های پروژه را کپی کنید
```

### مرحله 2: Build Commands
```bash
# Terminal در Replit
flutter pub get
flutter build web --release

# Web version آماده می‌شود
# URL: https://your-repl-name.username.repl.co
```

## 📱 روش 4: APK Builder Online

### سایت‌های پیشنهادی:
```
1. ApkOnline.com
2. BuildAPK.com  
3. FlutterFlow.io
4. AppGyver.com
```

### مراحل:
```
1. فایل‌های پروژه را ZIP کنید
2. سایت را باز کنید
3. ZIP را آپلود کنید
4. Build را شروع کنید
5. APK را دانلود کنید
```

## 🎯 توصیه نهایی: GitHub Actions

**چرا GitHub Actions بهترین است:**
- ✅ کاملاً رایگان
- ✅ سریع (20 دقیقه)
- ✅ قابل اعتماد
- ✅ تکرارپذیر
- ✅ لینک مستقیم دانلود

### مراحل سریع:
```
1. کد را به GitHub آپلود کنید (5 دقیقه)
2. GitHub Actions را فعال کنید (2 دقیقه)  
3. Build منتظر بمانید (20 دقیقه)
4. APK دانلود کنید (1 دقیقه)
5. لینک را با سربازان شیر کنید (1 دقیقه)

کل زمان: 30 دقیقه
```

## 📲 لینک نهایی برای سربازان

پس از build موفق:
```
🔗 لینک دانلود APK:
https://github.com/username/partizan-app/releases/download/v1.0/app-release.apk

📱 راهنمای نصب:
1. لینک را باز کنید
2. "Download" کلیک کنید  
3. APK را نصب کنید
4. مجوزها را بدهید
5. با حساب asker/partizan2024 وارد شوید

✅ آماده استفاده!
```

کدام روش را ترجیح می‌دهید؟
